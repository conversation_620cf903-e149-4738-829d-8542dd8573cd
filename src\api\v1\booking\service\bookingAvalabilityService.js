// /* eslint-disable id-length */
// /* eslint-disable prefer-const */
// const ServiceAvailability = require('../../serviceInfo/model/serviceAvailabilityModel');

// const Booking = require('../model/bookingModel');

// const Service = require('../../serviceInfo/service/serviceInfoService');

// const Staff = require('../../staff/model/staffModel'); //

// const AvailabilityLog = require('../model/bookingAvalabilityModel');

// const logger = require('../../../common/utils/logger');

// const {
//     getHolidaysByStaffId,
// } = require('../../staff/service/staffHolidayService');

// function timeToMinutes(timeStr) {
//     if (!timeStr || typeof timeStr !== 'string') {
//         throw new Error(`Invalid time string: ${timeStr}`);
//     }

//     const [time, modifier] = timeStr.split(/(AM|PM)/i);
//     let [hours, minutes] = time.trim().split(':').map(Number);
//     if (modifier?.toUpperCase() === 'PM' && hours !== 12) hours += 12;
//     if (modifier?.toUpperCase() === 'AM' && hours === 12) hours = 0;
//     return hours * 60 + minutes;
// }

// // Get weekday name from date string
// function getDayOfWeek(dateStr) {
//     return new Date(dateStr)
//         .toLocaleDateString('en-US', { weekday: 'long' })
//         .toLowerCase();
// }

// // ✅ 1. Check if a specific slot is available
// async function checkAvailability({ serviceId, date, from, to }) {
//     try {
//         const parsedDate = new Date(date);
//         const day = getDayOfWeek(date);
//         const fromMin = timeToMinutes(from);
//         const toMin = timeToMinutes(to);

//         logger.info(
//             `Checking availability for service ${serviceId} on ${date} from ${from} to ${to}`
//         );

//         const availability = await ServiceAvailability.findOne({
//             serviceId,
//             day,
//         });
//         if (!availability?.available) {
//             logger.warn(`Service ${serviceId} is not available on ${day}`);
//             return null;
//         }

//         const selectedSlot = availability.timeSlots.find(
//             (s) =>
//                 timeToMinutes(s.from) === fromMin &&
//                 timeToMinutes(s.to) === toMin
//         );
//         if (!selectedSlot) {
//             logger.warn(
//                 `Time slot from ${from} to ${to} not found for service ${serviceId}`
//             );
//             return null;
//         }

//         const bookings = await Booking.find({
//             serviceId,
//             appointmentDate: parsedDate,
//             appointmentTimeSlot: from,
//             bookingStatus: { $in: ['Pending', 'Confirmed'] },
//         });

//         const booked = bookings.length;
//         const available = booked < selectedSlot.maxBookings;

//         logger.info(
//             `Availability for ${from} to ${to}: ${available ? 'Available' : 'Not Available'}, Booked: ${booked}`
//         );

//         return {
//             available,
//             timeSlot: {
//                 from: selectedSlot.from,
//                 to: selectedSlot.to,
//                 maxBookings: selectedSlot.maxBookings,
//                 booked,
//             },
//         };
//     } catch (error) {
//         logger.error(`Error checking availability: ${error.message}`);
//         throw error;
//     }
// }

// // ✅ 2. Book a time slot (ensures no duplicates)

// async function bookTimeSlot({
//     serviceId,
//     date,
//     from,
//     to,
//     bookingId = 1,
//     staffId = 1,
//     referenceCode = 'REF12345',
// }) {
//     try {
//         const parsedDate = new Date(date);
//         const day = getDayOfWeek(date);

//         logger.info(
//             `Booking time slot for service ${serviceId} on ${date} from ${from} to ${to} for staff ${staffId}`
//         );

//         const availabilityCheck = await checkAvailability({
//             serviceId,
//             date,
//             from,
//             to,
//         });

//         if (!availabilityCheck?.available) {
//             logger.warn(
//                 `Time slot from ${from} to ${to} is not available for service ${serviceId}`
//             );
//             return { success: false, message: 'Time slot not available.' };
//         }

//         // 📌 Prepare log entry with bookedStaffId
//         const slotLog = {
//             bookingId,
//             referenceCode,
//             from,
//             to,
//             maxBookings: 1,
//             booked: 1,
//             bookingDate: parsedDate,
//             bookedStaffId: staffId, // ✅ Important
//             bookingStatus: 'booked',
//         };

//         // 🛠 Try to update existing time slot in AvailabilityLog
//         const updated = await AvailabilityLog.updateOne(
//             {
//                 serviceId,
//                 date: parsedDate,
//                 'timeSlots.from': from,
//                 'timeSlots.to': to,
//             },
//             {
//                 $set: {
//                     'timeSlots.$.booked': 1,
//                     'timeSlots.$.bookingId': bookingId,
//                     'timeSlots.$.referenceCode': referenceCode,
//                     'timeSlots.$.bookingDate': parsedDate,
//                     'timeSlots.$.bookedStaffId': staffId,
//                     'timeSlots.$.bookingStatus': 'booked',
//                 },
//                 $setOnInsert: { available: true, day },
//             }
//         );

//         if (updated.modifiedCount === 0) {
//             await AvailabilityLog.updateOne(
//                 { serviceId, date: parsedDate },
//                 {
//                     $push: { timeSlots: slotLog },
//                     $setOnInsert: { available: true, day },
//                 },
//                 { upsert: true }
//             );
//             logger.info(`New time slot log created for ${from} - ${to}`);
//         }

//         return {
//             success: true,
//             bookingId,
//             timeSlot: slotLog,
//         };
//     } catch (error) {
//         logger.error(`Error booking time slot: ${error.message}`);
//         throw error;
//     }
// }

// // async function getAvailableTimeSlots({ serviceId, date, staffId }) {
// //     try {
// //         const parsedDate = new Date(date);
// //         const day = getDayOfWeek(date);

// //         logger.info(
// //             `Getting available time slots for service ${serviceId} on ${date}${staffId ? ' for staff ' + staffId : ''}`
// //         );

// //         const availability = await ServiceAvailability.findOne({
// //             serviceId,
// //             day,
// //         });

// //         if (!availability?.available) {
// //             logger.warn(`Service ${serviceId} has no availability for ${day}`);
// //             return {
// //                 success: false,
// //                 message: 'No availability for this service',
// //             };
// //         }

// //         const allSlots = availability.timeSlots;
// //         const availabilityLog = await AvailabilityLog.findOne({
// //             serviceId,
// //             date: parsedDate,
// //         });

// //         const bookedSlots = new Map();

// //         if (availabilityLog?.timeSlots?.length) {
// //             availabilityLog.timeSlots.forEach((slot) => {
// //                 const key = `${slot.from}-${slot.to}`;
// //                 if (
// //                     slot.booked >= slot.maxBookings ||
// //                     slot.bookingStatus === 'booked'
// //                 ) {
// //                     bookedSlots.set(key, slot);
// //                 }
// //             });
// //         }

// //         const formattedSlots = allSlots.map((slot) => {
// //             const key = `${slot.from}-${slot.to}`;
// //             const bookedInfo = bookedSlots.get(key);

// //             let isBookedByThisStaff = false;
// //             let staffStatus = 'available';

// //             if (bookedInfo) {
// //                 if (staffId && bookedInfo.bookedStaffId === staffId) {
// //                     isBookedByThisStaff = true;
// //                     staffStatus = 'unavailable';
// //                 }
// //             }

// //             // Extract bookingStatus logic to a separate variable
// //             let bookingStatus = 'available';
// //             if (bookedInfo) {
// //                 bookingStatus = isBookedByThisStaff ? 'booked' : 'available';
// //             }

// //             return {
// //                 timeSlotId: generateTimeSlotId(),
// //                 bookingId: isBookedByThisStaff ? bookedInfo.bookingId : 'null',
// //                 from: slot.from,
// //                 to: slot.to,
// //                 maxBookings: slot.maxBookings,
// //                 booked: isBookedByThisStaff ? bookedInfo.booked : 0,
// //                 bookingDate: isBookedByThisStaff
// //                     ? bookedInfo.bookingDate
// //                     : null,
// //                 bookedStaffId: isBookedByThisStaff
// //                     ? bookedInfo.bookedStaffId
// //                     : null,
// //                 bookingStatus: bookingStatus,
// //                 ...(staffId && { staffStatus }),
// //             };
// //         });

// //         logger.info(
// //             `Retrieved available time slots for service ${serviceId} on ${date}${staffId ? ' for staff ' + staffId : ''}`
// //         );

// //         return {
// //             success: true,
// //             serviceId,
// //             date,
// //             timeSlots: formattedSlots,
// //         };
// //     } catch (error) {
// //         logger.error(`Error retrieving available time slots: ${error.message}`);
// //         throw error;
// //     }
// // }
// async function getAvailableTimeSlots({ serviceId, date, staffId }) {
//     try {
//         const parsedDate = new Date(date);
//         const parsedDateOnly = parsedDate.toISOString().split('T')[0]; // For accurate holiday comparison
//         const day = getDayOfWeek(date);

//         logger.info(
//             `Getting available time slots for service ${serviceId} on ${date}${staffId ? ' for staff ' + staffId : ''}`
//         );

//         const availability = await ServiceAvailability.findOne({
//             serviceId,
//             day,
//         });

//         if (!availability?.available) {
//             logger.warn(`Service ${serviceId} has no availability for ${day}`);
//             return {
//                 success: false,
//                 message: 'No availability for this service',
//             };
//         }

//         const allSlots = availability.timeSlots;
//         const availabilityLog = await AvailabilityLog.findOne({
//             serviceId,
//             date: parsedDate,
//         });

//         const bookedSlotsMap = new Map();

//         if (availabilityLog?.timeSlots?.length) {
//             availabilityLog.timeSlots.forEach((slot) => {
//                 const key = `${slot.from}-${slot.to}`;
//                 if (!bookedSlotsMap.has(key)) {
//                     bookedSlotsMap.set(key, []);
//                 }
//                 bookedSlotsMap.get(key).push(slot);
//             });
//         }

//         // Check staff holidays
//         if (staffId) {
//             const holidays = await getHolidaysByStaffId(staffId);
//             const isOnHoliday = holidays.some((h) =>
//                 isDateInHolidayRange(parsedDateOnly, h)
//             );

//             if (isOnHoliday) {
//                 logger.warn(`Staff ${staffId} is on holiday on ${date}`);
//                 return {
//                     success: false,
//                     message: 'Staff is on holiday on the selected date',
//                 };
//             }

//             const isAvailable = isStaffAvailable(
//                 { staffId },
//                 holidays,
//                 parsedDateOnly,
//                 availability,
//                 bookedSlotsMap
//             );

//             if (!isAvailable) {
//                 logger.warn(
//                     `Staff ${staffId} has no available slots on ${date}`
//                 );
//                 return {
//                     success: false,
//                     message:
//                         'No available time slots for this staff on selected date',
//                 };
//             }
//         }

//         const formattedSlots = allSlots.map((slot) => {
//             const key = `${slot.from}-${slot.to}`;
//             const bookings = bookedSlotsMap.get(key) || [];

//             const staffBooking = bookings.find(
//                 (b) => b.bookedStaffId === staffId
//             );

//             const isBookedByThisStaff = staffBooking !== undefined;

//             let bookingStatus = 'available';
//             if (staffBooking) {
//                 bookingStatus =
//                     staffBooking.booked >= slot.maxBookings ||
//                     staffBooking.bookingStatus === 'booked'
//                         ? 'booked'
//                         : 'available';
//             }

//             const staffStatus = staffId
//                 ? isBookedByThisStaff && bookingStatus === 'booked'
//                     ? 'unavailable'
//                     : 'available'
//                 : undefined;

//             return {
//                 timeSlotId: generateTimeSlotId(),
//                 bookingId: isBookedByThisStaff
//                     ? staffBooking.bookingId
//                     : 'null',
//                 from: slot.from,
//                 to: slot.to,
//                 maxBookings: slot.maxBookings,
//                 booked: isBookedByThisStaff ? staffBooking.booked : 0,
//                 bookingDate: isBookedByThisStaff
//                     ? staffBooking.bookingDate
//                     : null,
//                 bookedStaffId: isBookedByThisStaff
//                     ? staffBooking.bookedStaffId
//                     : null,
//                 bookingStatus,
//                 ...(staffId && { staffStatus }),
//             };
//         });

//         logger.info(
//             `Retrieved available time slots for service ${serviceId} on ${date}${staffId ? ' for staff ' + staffId : ''}`
//         );

//         return {
//             success: true,
//             serviceId,
//             date,
//             timeSlots: formattedSlots,
//         };
//     } catch (error) {
//         logger.error(`Error retrieving available time slots: ${error.message}`);
//         throw error;
//     }
// }

// function isDateInHolidayRange(dateStr, holiday) {
//     const target = new Date(dateStr);
//     const start = new Date(holiday.startDate);
//     const end = new Date(holiday.endDate);
//     return holiday.approved && target >= start && target <= end;
// }

// async function getStaffHolidayMap(staffDetails) {
//     const holidayMap = new Map();
//     for (const staff of staffDetails) {
//         const holidays = await getHolidaysByStaffId(staff.staffId);
//         holidayMap.set(staff.staffId, holidays);
//     }
//     return holidayMap;
// }

// function isStaffAvailable(
//     staff,
//     holidays,
//     parsedDateOnly,
//     availability,
//     bookedSlotsMap
// ) {
//     const isOnHoliday = holidays.some((h) =>
//         isDateInHolidayRange(parsedDateOnly, h)
//     );
//     if (isOnHoliday) return false;

//     return availability.timeSlots.some((slot) => {
//         const key = `${slot.from}-${slot.to}`;
//         const bookings = bookedSlotsMap.get(key) || [];
//         const staffBooking = bookings.find(
//             (b) => b.bookedStaffId === staff.staffId
//         );
//         return !(
//             staffBooking &&
//             (staffBooking.booked >= slot.maxBookings ||
//                 staffBooking.bookingStatus === 'booked')
//         );
//     });
// }

// async function getAvailableStaff(serviceId, date) {
//     try {
//         const parsedDate = new Date(date);
//         const parsedDateOnly = parsedDate.toISOString().split('T')[0];
//         const day = getDayOfWeek(parsedDate);

//         const service = await Service.getServiceInformationById(serviceId);
//         if (!service) {
//             return { success: false, message: 'Service not found' };
//         }

//         const staffIds = service.staff || [];
//         if (staffIds.length === 0) {
//             return {
//                 success: true,
//                 serviceId,
//                 date,
//                 usesStaff: false,
//                 availableStaff: [],
//             };
//         }

//         const [staffDetails, availability, availabilityLog] = await Promise.all(
//             [
//                 Staff.find({ staffId: { $in: staffIds } }),
//                 ServiceAvailability.findOne({ serviceId, day }),
//                 AvailabilityLog.findOne({ serviceId, date: parsedDate }),
//             ]
//         );

//         if (!staffDetails.length) {
//             return {
//                 success: false,
//                 message: 'No staff found for this service',
//             };
//         }

//         if (!availability?.available) {
//             return {
//                 success: false,
//                 message: 'Service is not available on this day',
//             };
//         }

//         const bookedSlotsMap = new Map();
//         if (availabilityLog?.timeSlots?.length) {
//             for (const slot of availabilityLog.timeSlots) {
//                 const key = `${slot.from}-${slot.to}`;
//                 if (!bookedSlotsMap.has(key)) {
//                     bookedSlotsMap.set(key, []);
//                 }
//                 bookedSlotsMap.get(key).push(slot);
//             }
//         }

//         const holidayMap = await getStaffHolidayMap(staffDetails);

//         const availableStaff = staffDetails
//             .filter((staff) =>
//                 isStaffAvailable(
//                     staff,
//                     holidayMap.get(staff.staffId) || [],
//                     parsedDateOnly,
//                     availability,
//                     bookedSlotsMap
//                 )
//             )
//             .map((staff) => ({
//                 staffId: staff.staffId,
//                 providerId: staff.providerId,
//                 providerStaffId: staff.providerStaffId,
//                 fullName: staff.fullName,
//                 numberOfCompletedServices: staff.numberOfCompletedServices || 0,
//                 city: staff.city,
//             }));

//         return {
//             success: true,
//             serviceId,
//             date,
//             usesStaff: true,
//             availableStaff,
//         };
//     } catch (error) {
//         logger.error(`Error in getAvailableStaff: ${error.message}`);
//         throw new Error('Failed to retrieve available staff');
//     }
// }

// async function deleteBookingTimeSlots({
//     serviceId,
//     appointmentDate,
//     bookingId,
// }) {
//     try {
//         logger.info(
//             `Attempting to remove time slot for booking ID: ${bookingId}`
//         );

//         const result = await AvailabilityLog.updateOne(
//             {
//                 serviceId,
//                 date: appointmentDate,
//                 'timeSlots.bookingId': bookingId, // This is CRUCIAL
//             },
//             {
//                 $set: {
//                     'timeSlots.$.booked': 0, // Should be 0, not 1, if you want it "available"
//                     'timeSlots.$.bookingId': null,
//                     'timeSlots.$.bookingDate': null,
//                     'timeSlots.$.bookedStaffId': null,
//                     'timeSlots.$.bookingStatus': 'available',
//                 },
//             }
//         );

//         if (result.modifiedCount === 0) {
//             logger.warn(
//                 `No matching time slot found for booking ID ${bookingId}`
//             );
//             return {
//                 success: false,
//                 message: 'Time slot not found or already reset.',
//             };
//         }

//         logger.info(
//             `Time slot reset for service ${serviceId} on ${appointmentDate}`
//         );
//         return { success: true, message: 'Time slot reset successfully.' };
//     } catch (error) {
//         logger.error(`Error resetting time slot: ${error.message}`);
//         throw new Error(`Failed to reset time slot: ${error.message}`);
//     }
// }

// function generateTimeSlotId() {
//     const randomNum = Math.floor(1000 + Math.random() * 9000);
//     return `TSID_${randomNum}`;
// }

// module.exports = {
//     checkAvailability,
//     bookTimeSlot,
//     getAvailableTimeSlots,
//     deleteBookingTimeSlots,
//     getAvailableStaff,
// };

/* eslint-disable id-length */
/* eslint-disable prefer-const */

const ServiceAvailability = require('../../serviceInfo/model/serviceAvailabilityModel');

const Booking = require('../model/bookingModel');

const Service = require('../../serviceInfo/service/serviceInfoService');

const Staff = require('../../staff/model/staffModel');

const AvailabilityLog = require('../model/bookingAvalabilityModel');

const logger = require('../../../common/utils/logger');

const {
    getHolidaysByStaffId,
} = require('../../staff/service/staffHolidayService');

const crypto = require('crypto');

const moment = require('moment');

//
// 🔧 Utility Functions
//

function timeToMinutes(timeStr) {
    if (!timeStr || typeof timeStr !== 'string') {
        throw new Error(`Invalid time string: ${timeStr}`);
    }
    const [time, modifier] = timeStr.split(/(AM|PM)/i);
    let [hours, minutes] = time.trim().split(':').map(Number);
    if (modifier?.toUpperCase() === 'PM' && hours !== 12) hours += 12;
    if (modifier?.toUpperCase() === 'AM' && hours === 12) hours = 0;
    return hours * 60 + minutes;
}

function getDayOfWeek(dateStr) {
    return new Date(dateStr)
        .toLocaleDateString('en-US', { weekday: 'long' })
        .toLowerCase();
}

function isDateInHolidayRange(dateStr, holiday) {
    const target = new Date(dateStr);
    const start = new Date(holiday.startDate);
    const end = new Date(holiday.endDate);
    return holiday.approved && target >= start && target <= end;
}

function generateTimeSlotId(date, index) {
    const parsedDate = new Date(date);
    const dateStr = parsedDate.toISOString().split('T')[0];
    const raw = `${dateStr}-${index}`;
    const hash = crypto
        .createHash('md5')
        .update(raw)
        .digest('hex')
        .slice(0, 10);
    return `TSID_${hash}`;
}

async function fetchServiceWithStaff(serviceId) {
    const service = await Service.getServiceInformationById(serviceId);
    if (!service) throw new Error('Service not found');
    const staffIds = service.staff || [];

    const staffList = staffIds.length
        ? await Staff.find({ staffId: { $in: staffIds } })
        : [];

    return { service, staffList, staffIds };
}

async function getAvailableStaffList(
    staffList,
    parsedDateOnly,
    availability,
    bookedSlotsMap = new Map()
) {
    const holidayMap = await getStaffHolidayMap(staffList);

    return staffList.filter((staff) =>
        isStaffAvailable(
            staff,
            holidayMap.get(staff.staffId) || [],
            parsedDateOnly,
            availability,
            bookedSlotsMap
        )
    );
}

function getStaffBookingsMap(bookings) {
    const map = new Map();
    for (const booking of bookings) {
        const staffId = booking.staffId;
        if (!map.has(staffId)) map.set(staffId, []);
        map.get(staffId).push({
            from: timeToMinutes(booking.appointmentTimeFrom),
            to: timeToMinutes(booking.appointmentTimeTo),
        });
    }
    return map;
}

function filterAvailableStaffForTimeSlot(
    slotFrom,
    slotTo,
    staffList,
    staffBookingMap
) {
    return staffList.filter((staff) => {
        const bookings = staffBookingMap.get(staff.staffId) || [];
        return !bookings.some(
            (booking) => slotFrom < booking.to && slotTo > booking.from
        );
    });
}

//
// ✅ Availability Check
//

async function checkAvailability({ serviceId, date, from, to }) {
    try {
        const parsedDate = new Date(date);
        const day = getDayOfWeek(date);
        const fromMin = timeToMinutes(from);
        const toMin = timeToMinutes(to);

        logger.info(
            `Checking availability for service ${serviceId} on ${date} from ${from} to ${to}`
        );

        const availability = await ServiceAvailability.findOne({
            serviceId,
            day,
        });
        if (!availability?.available) return null;

        const selectedSlot = availability.timeSlots.find(
            (s) =>
                timeToMinutes(s.from) === fromMin &&
                timeToMinutes(s.to) === toMin
        );
        if (!selectedSlot) return null;

        const bookings = await Booking.find({
            serviceId,
            appointmentDate: parsedDate,
            appointmentTimeSlot: from,
            bookingStatus: { $in: ['Pending', 'Confirmed'] },
        });

        const booked = bookings.length;
        const available = booked < selectedSlot.maxBookings;

        return {
            available,
            timeSlot: {
                from: selectedSlot.from,
                to: selectedSlot.to,
                maxBookings: selectedSlot.maxBookings,
                booked,
            },
        };
    } catch (error) {
        logger.error(`Error checking availability: ${error.message}`);
        throw error;
    }
}

//
// ✅ Book Time Slot
//

async function bookTimeSlot({
    serviceId,
    date,
    from,
    to,
    bookingId,
    staffId,
    referenceCode,
    timeSlotId = 12,
}) {
    try {
        const parsedDate = new Date(date);
        const day = getDayOfWeek(date);

        logger.info(
            `Booking time slot for service ${serviceId} on ${date} from ${from} to ${to} for staff ${staffId}`
        );

        const availabilityCheck = await checkAvailability({
            serviceId,
            date,
            from,
            to,
        });
        if (!availabilityCheck?.available) {
            return { success: false, message: 'Time slot not available.' };
        }

        const slotLog = {
            bookingId,
            referenceCode,
            timeSlotId,
            from,
            to,
            maxBookings: 1,
            booked: 1,
            bookingDate: parsedDate,
            bookedStaffId: staffId,
            bookingStatus: 'booked',
        };

        const updated = await AvailabilityLog.updateOne(
            {
                serviceId,
                date: parsedDate,
                'timeSlots.from': from,
                'timeSlots.to': to,
            },
            {
                $set: {
                    'timeSlots.$.booked': 1,
                    'timeSlots.$.bookingId': bookingId,
                    'timeSlots.$.referenceCode': referenceCode,
                    'timeSlots.$.timeSlotId': timeSlotId,
                    'timeSlots.$.bookingDate': parsedDate,
                    'timeSlots.$.bookedStaffId': staffId,
                    'timeSlots.$.bookingStatus': 'booked',
                },
                $setOnInsert: { available: true, day },
            }
        );

        if (updated.modifiedCount === 0) {
            await AvailabilityLog.updateOne(
                { serviceId, date: parsedDate },
                {
                    $push: { timeSlots: slotLog },
                    $setOnInsert: { available: true, day },
                },
                { upsert: true }
            );
        }

        return { success: true, bookingId, timeSlot: slotLog };
    } catch (error) {
        logger.error(`Error booking time slot: ${error.message}`);
        throw error;
    }
}

//
// 🧠 Staff Availability
//

function isStaffAvailable(
    staff,
    holidays,
    parsedDateOnly,
    availability,
    bookedSlotsMap
) {
    const isOnHoliday = holidays.some((h) =>
        isDateInHolidayRange(parsedDateOnly, h)
    );
    if (isOnHoliday) return false;

    return availability.timeSlots.some((slot) => {
        const key = `${slot.from}-${slot.to}`;
        const bookings = bookedSlotsMap.get(key) || [];
        const staffBooking = bookings.find(
            (b) => b.bookedStaffId === staff.staffId
        );
        return !(
            staffBooking &&
            (staffBooking.booked >= slot.maxBookings ||
                staffBooking.bookingStatus === 'booked')
        );
    });
}

async function getStaffHolidayMap(staffDetails) {
    const holidayMap = new Map();
    for (const staff of staffDetails) {
        const holidays = await getHolidaysByStaffId(staff.staffId);
        holidayMap.set(staff.staffId, holidays);
    }
    return holidayMap;
}

function isOverlapping(start1, end1, start2, end2) {
    return start1 < end2 && start2 < end1;
}

async function getAvailableTimeSlots({ serviceId, date, staffId }) {
    try {
        const parsedDate = new Date(date);
        const day = getDayOfWeek(date);

        const availability = await ServiceAvailability.findOne({
            serviceId,
            day,
        });
        if (!availability?.available) {
            return {
                success: false,
                message: 'No availability for this service',
            };
        }

        const availabilityLog = await AvailabilityLog.findOne({
            serviceId,
            date: parsedDate,
        });

        const bookedSlotsMap = new Map();
        if (availabilityLog?.timeSlots?.length) {
            for (const slot of availabilityLog.timeSlots) {
                const key = `${slot.from}-${slot.to}`;
                if (!bookedSlotsMap.has(key)) bookedSlotsMap.set(key, []);
                bookedSlotsMap.get(key).push(slot);
            }
        }

        let staffBookedRanges = [];
        if (staffId) {
            const bookedTimeSet = await getStaffBookedSlotsOnDate(
                staffId,
                date
            );
            staffBookedRanges = Array.from(bookedTimeSet).map((rangeStr) => {
                const [fromStr, toStr] = rangeStr.split('-');
                return {
                    from: timeToMinutes(fromStr),
                    to: timeToMinutes(toStr),
                };
            });
        }

        const formattedSlots = availability.timeSlots
            .map((slot, index) => {
                const key = `${slot.from}-${slot.to}`;
                const bookings = bookedSlotsMap.get(key) || [];

                const staffBooking = bookings.find(
                    (b) => b.bookedStaffId === staffId
                );
                const isBookedByThisStaff = !!staffBooking;

                const bookingStatus =
                    staffBooking &&
                    (staffBooking.booked >= slot.maxBookings ||
                        staffBooking.bookingStatus === 'booked')
                        ? 'booked'
                        : 'available';

                const slotFromMins = timeToMinutes(slot.from);
                const slotToMins = timeToMinutes(slot.to);

                const isOverlappingStaffBooking = staffBookedRanges.some(
                    (bRange) =>
                        isOverlapping(
                            slotFromMins,
                            slotToMins,
                            bRange.from,
                            bRange.to
                        )
                );

                const staffStatus = staffId
                    ? isOverlappingStaffBooking
                        ? 'unavailable'
                        : 'available'
                    : undefined;

                return {
                    timeSlotId: generateTimeSlotId(date, index),
                    bookingId: isBookedByThisStaff
                        ? staffBooking.bookingId
                        : null,
                    referenceCode: isBookedByThisStaff
                        ? staffBooking.referenceCode
                        : null,
                    from: slot.from,
                    to: slot.to,
                    maxBookings: slot.maxBookings,
                    booked: isBookedByThisStaff ? staffBooking.booked : 0,
                    bookingDate: isBookedByThisStaff
                        ? staffBooking.bookingDate
                        : null,
                    bookedStaffId: isBookedByThisStaff
                        ? staffBooking.bookedStaffId
                        : null,
                    bookingStatus,
                    ...(staffId && { staffStatus }),
                };
            })
            // FILTER OUT slots where staffStatus is 'unavailable'
            .filter((slot) => slot.staffStatus !== 'unavailable');

        return { success: true, serviceId, date, timeSlots: formattedSlots };
    } catch (error) {
        logger.error(`Error retrieving available time slots: ${error.message}`);
        throw error;
    }
}

async function getStaffBookedSlotsOnDate(staffId, date) {
    const logs = await AvailabilityLog.find({
        date: new Date(date),
        'timeSlots.bookedStaffId': staffId,
        'timeSlots.bookingStatus': 'booked',
    });

    const bookedTimeRanges = new Set();
    for (const log of logs) {
        for (const slot of log.timeSlots) {
            if (
                slot.bookedStaffId === staffId &&
                slot.bookingStatus === 'booked'
            ) {
                bookedTimeRanges.add(`${slot.from}-${slot.to}`);
            }
        }
    }

    return bookedTimeRanges;
}

// 📅 Generate date list
function generateDateRange(start, end) {
    const dates = [];
    let current = moment(start);
    while (current.isSameOrBefore(end)) {
        dates.push(current.format('YYYY-MM-DD'));
        current.add(1, 'day');
    }
    return dates;
}

// 🔍 Check each date using .map and Promise.all
async function checkAvailabilityDateRange(serviceId, startDate, endDate) {
    try {
        const dateList = generateDateRange(startDate, endDate);

        const availabilityResults = await Promise.all(
            dateList.map(async (date) => {
                try {
                    const result = await getAvailableStaff(serviceId, date);
                    if (
                        !result.success ||
                        (result.usesStaff && result.availableStaff.length === 0)
                    ) {
                        return null; // not available
                    } else {
                        return date; // available
                    }
                } catch (innerError) {
                    logger.error(
                        `Error checking availability for ${date}:`,
                        innerError
                    );
                    return null; // Treat this date as unavailable if an error occurs
                }
            })
        );

        const availableDates = availabilityResults.filter(
            (date) => date !== null
        );
        return {
            success: true,
            message: 'successful to check availability for the date range.',
            availableDates: availableDates,
        };
    } catch (error) {
        logger.error('An error occurred in checkAvailabilityDateRange:', error);
        return {
            success: false,
            message: 'Failed to check availability for the date range.',
        };
    }
}

// async function getAvailableStaff(serviceId, date) {
//     try {
//         const parsedDate = new Date(date);
//         const parsedDateOnly = parsedDate.toISOString().split('T')[0];
//         const day = getDayOfWeek(date);

//         const service = await Service.getServiceInformationById(serviceId);
//         if (!service) return { success: false, message: 'Service not found' };

//         const staffIds = service.staff || [];
//         if (!staffIds.length) {
//             return {
//                 success: true,
//                 serviceId,
//                 date,
//                 usesStaff: false,
//                 availableStaff: [],
//             };
//         }

//         const [staffDetails, availability, availabilityLog] = await Promise.all(
//             [
//                 Staff.find({ staffId: { $in: staffIds } }),
//                 ServiceAvailability.findOne({ serviceId, day }),
//                 AvailabilityLog.findOne({ serviceId, date: parsedDate }),
//             ]
//         );

//         if (!availability?.available)
//             return {
//                 success: false,
//                 message: 'Service not available on this day',
//             };

//         const bookedSlotsMap = new Map();
//         if (availabilityLog?.timeSlots?.length) {
//             for (const slot of availabilityLog.timeSlots) {
//                 const key = `${slot.from}-${slot.to}`;
//                 if (!bookedSlotsMap.has(key)) bookedSlotsMap.set(key, []);
//                 bookedSlotsMap.get(key).push(slot);
//             }
//         }

//         const holidayMap = await getStaffHolidayMap(staffDetails);

//         const availableStaff = staffDetails
//             .filter((staff) =>
//                 isStaffAvailable(
//                     staff,
//                     holidayMap.get(staff.staffId) || [],
//                     parsedDateOnly,
//                     availability,
//                     bookedSlotsMap
//                 )
//             )
//             .map((staff) => ({
//                 staffId: staff.staffId,
//                 providerId: staff.providerId,
//                 providerStaffId: staff.providerStaffId,
//                 fullName: staff.fullName,
//                 numberOfCompletedServices: staff.numberOfCompletedServices || 0,
//                 city: staff.city,
//             }));

//         return {
//             success: true,
//             serviceId,
//             date,
//             usesStaff: true,
//             availableStaff,
//         };
//     } catch (error) {
//         logger.error(`Error in getAvailableStaff: ${error.message}`);
//         throw new Error('Failed to retrieve available staff');
//     }
// }

// async function getAvailableTimeSlotsWithStaff(serviceId, date) {
//     try {
//         // Step 0: Check if date is fully available with all time slots first
//         const dateAvailability = await isDateFullyAvailableWithAllTimeSlots(
//             serviceId,
//             date
//         );

//         if (!dateAvailability.success) {
//             return dateAvailability;
//         }

//         const parsedDate = new Date(date);
//         const parsedDateOnly = parsedDate.toISOString().split('T')[0];

//         // 1. Get service info & staff list for this service
//         const service = await Service.getServiceInformationById(serviceId);
//         if (!service || !service.staff?.length) {
//             return {
//                 success: false,
//                 message: 'No staff assigned to this service',
//             };
//         }
//         const staffIds = service.staff;

//         // 2. Fetch staff, holidays, and all bookings (all services) for these staff on this date
//         const [staffList, holidaysMap, bookings] = await Promise.all([
//             Staff.find({ staffId: { $in: staffIds } }),
//             getStaffHolidayMap(
//                 await Staff.find({ staffId: { $in: staffIds } })
//             ),
//             Booking.find({
//                 staffId: { $in: staffIds },
//                 appointmentDate: parsedDate,
//                 bookingStatus: { $in: ['Pending', 'Confirmed'] },
//             }),
//         ]);

//         const allTimeSlots = dateAvailability.timeSlots;

//         // 3. Filter out staff on leave or holiday for this date
//         const availableStaff = staffList.filter((staff) => {
//             const holidays = holidaysMap.get(staff.staffId) || [];
//             return !holidays.some((holiday) =>
//                 isDateInHolidayRange(parsedDateOnly, holiday)
//             );
//         });

//         if (!availableStaff.length) {
//             return {
//                 success: false,
//                 message: 'No staff available on this day',
//             };
//         }

//         // Helper to convert "2:30 PM" => minutes from midnight (e.g. 14*60 + 30 = 870)
//         // Assuming you have this already
//         // function timeToMinutes(timeStr) { ... }

//         // 4. Build map of bookings by staff with exact from-to minutes ranges (real booking ranges)
//         const staffBookingMap = new Map();

//         for (const booking of bookings) {
//             const staffId = booking.staffId;
//             if (!staffBookingMap.has(staffId)) staffBookingMap.set(staffId, []);

//             // Convert booking appointmentTimeFrom and appointmentTimeTo to minutes
//             const fromMins = timeToMinutes(booking.appointmentTimeFrom);
//             const toMins = timeToMinutes(booking.appointmentTimeTo);

//             staffBookingMap.get(staffId).push({ from: fromMins, to: toMins });
//         }

//         // 5. Now prepare the available time slots with staff who have no booking overlap
//         const timeSlotStaffMap = [];

//         for (const [index, slot] of allTimeSlots.entries()) {
//             const slotFrom = timeToMinutes(slot.from);
//             const slotTo = timeToMinutes(slot.to);

//             const freeStaff = availableStaff.filter((staff) => {
//                 const bookings = staffBookingMap.get(staff.staffId) || [];

//                 // Check if this time slot overlaps with any existing booking of this staff
//                 for (const booking of bookings) {
//                     // overlap if slotFrom < booking.to AND slotTo > booking.from
//                     if (slotFrom < booking.to && slotTo > booking.from) {
//                         return false; // overlap found, not free
//                     }
//                 }

//                 return true; // no overlap found, free
//             });

//             timeSlotStaffMap.push({
//                 timeSlotId: generateTimeSlotId(date, index),
//                 from: slot.from,
//                 to: slot.to,
//                 availableStaff: freeStaff.map((st) => ({
//                     staffId: st.staffId,
//                     fullName: st.fullName,
//                     providerStaffId: st.providerStaffId,
//                 })),
//             });
//         }

//         return {
//             success: true,
//             serviceId,
//             date,
//             timeSlots: timeSlotStaffMap,
//         };
//     } catch (error) {
//         logger.error(
//             `Error in getAvailableTimeSlotsWithStaff: ${error.message}`
//         );
//         return {
//             success: false,
//             message: 'Failed to get available time slots with staff.',
//         };
//     }
// }

async function getAvailableStaff(serviceId, date) {
    try {
        const parsedDate = new Date(date);
        const parsedDateOnly = parsedDate.toISOString().split('T')[0];
        const day = getDayOfWeek(date);

        const { staffList, staffIds } = await fetchServiceWithStaff(serviceId);
        if (!staffIds.length) {
            return {
                success: true,
                serviceId,
                date,
                usesStaff: false,
                availableStaff: [],
            };
        }

        const [availability, availabilityLog] = await Promise.all([
            ServiceAvailability.findOne({ serviceId, day }),
            AvailabilityLog.findOne({ serviceId, date: parsedDate }),
        ]);

        if (!availability?.available) {
            return {
                success: false,
                message: 'Service not available on this day',
            };
        }

        const bookedSlotsMap = new Map();
        if (availabilityLog?.timeSlots?.length) {
            for (const slot of availabilityLog.timeSlots) {
                const key = `${slot.from}-${slot.to}`;
                if (!bookedSlotsMap.has(key)) bookedSlotsMap.set(key, []);
                bookedSlotsMap.get(key).push(slot);
            }
        }

        const availableStaff = await getAvailableStaffList(
            staffList,
            parsedDateOnly,
            availability,
            bookedSlotsMap
        );

        return {
            success: true,
            serviceId,
            date,
            usesStaff: true,
            availableStaff: availableStaff.map((staff) => ({
                staffId: staff.staffId,
                providerId: staff.providerId,
                providerStaffId: staff.providerStaffId,
                fullName: staff.fullName,
                numberOfCompletedServices: staff.numberOfCompletedServices || 0,
                city: staff.city,
            })),
        };
    } catch (error) {
        logger.error(`Error in getAvailableStaff: ${error.message}`);
        throw new Error('Failed to retrieve available staff');
    }
}

async function getAvailableTimeSlotsWithStaff(serviceId, date) {
    try {
        const dateAvailability = await isDateFullyAvailableWithAllTimeSlots(
            serviceId,
            date
        );
        if (!dateAvailability.success) return dateAvailability;

        const parsedDate = new Date(date);
        const parsedDateOnly = parsedDate.toISOString().split('T')[0];

        const { staffList, staffIds } = await fetchServiceWithStaff(serviceId);
        if (!staffIds.length) {
            return {
                success: false,
                message: 'No staff assigned to this service',
            };
        }

        const [holidaysMap, bookings] = await Promise.all([
            getStaffHolidayMap(staffList),
            Booking.find({
                staffId: { $in: staffIds },
                appointmentDate: parsedDate,
                bookingStatus: { $in: ['Pending', 'Confirmed'] },
            }),
        ]);

        const availableStaff = staffList.filter((staff) => {
            const holidays = holidaysMap.get(staff.staffId) || [];
            return !holidays.some((holiday) =>
                isDateInHolidayRange(parsedDateOnly, holiday)
            );
        });

        if (!availableStaff.length) {
            return {
                success: false,
                message: 'No staff available on this day',
            };
        }

        const staffBookingMap = getStaffBookingsMap(bookings);
        const timeSlotStaffMap = dateAvailability.timeSlots.map(
            (slot, index) => {
                const slotFrom = timeToMinutes(slot.from);
                const slotTo = timeToMinutes(slot.to);
                const freeStaff = filterAvailableStaffForTimeSlot(
                    slotFrom,
                    slotTo,
                    availableStaff,
                    staffBookingMap
                );

                return {
                    timeSlotId: generateTimeSlotId(date, index),
                    from: slot.from,
                    to: slot.to,
                    availableStaff: freeStaff.map((st) => ({
                        staffId: st.staffId,
                        fullName: st.fullName,
                        providerStaffId: st.providerStaffId,
                        isProvider: st.isProvider || false,
                    })),
                };
            }
        );

        return {
            success: true,
            serviceId,
            date,
            timeSlots: timeSlotStaffMap,
        };
    } catch (error) {
        logger.error(
            `Error in getAvailableTimeSlotsWithStaff: ${error.message}`
        );
        return {
            success: false,
            message: 'Failed to get available time slots with staff.',
        };
    }
}

async function isDateFullyAvailableWithAllTimeSlots(serviceId, date) {
    try {
        if (!serviceId || !date) {
            return {
                success: false,
                message: 'Missing serviceId or date',
            };
        }

        // Validate date
        const parsedDate = new Date(date);
        if (isNaN(parsedDate.getTime())) {
            return {
                success: false,
                message: 'Invalid date format',
            };
        }

        // getDayOfWeek expects a string or Date, make sure it matches the expected format
        const day = getDayOfWeek(date);
        if (!day) {
            return {
                success: false,
                message: 'Invalid day derived from date',
            };
        }

        // Query using primitive values only
        const availability = await ServiceAvailability.findOne({
            serviceId: String(serviceId),
            day: String(day),
        });

        if (!availability?.available) {
            return {
                success: false,
                message: `Service is not available on ${day}`,
            };
        }

        if (!availability.timeSlots || availability.timeSlots.length === 0) {
            return {
                success: false,
                message: `No time slots defined for ${day}`,
            };
        }
        logger.info(
            `Checking availability for service ${serviceId} on ${date} (${day})`
        );
        return {
            success: true,
            message: `Date is available and has full set of time slots`,
            date,
            day,
            serviceId,
            timeSlots: availability.timeSlots.map((slot, index) => ({
                timeSlotId: generateTimeSlotId(date, index),
                from: slot.from,
                to: slot.to,
                maxBookings: slot.maxBookings,
            })),
        };
    } catch (error) {
        logger.error(
            `Error in isDateFullyAvailableWithAllTimeSlots: ${error.message}`
        );
        return {
            success: false,
            message: 'Failed to check date availability with all time slots.',
        };
    }
}

//
// ✅ Get Available Staff for a Day
//

async function deleteBookingTimeSlots({
    serviceId,
    appointmentDate,
    bookingId,
}) {
    try {
        logger.info(
            `Attempting to remove time slot for booking ID: ${bookingId}`
        );

        const result = await AvailabilityLog.updateOne(
            {
                serviceId,
                date: appointmentDate,
                'timeSlots.bookingId': bookingId,
            },
            {
                $set: {
                    'timeSlots.$.booked': 0,
                    'timeSlots.$.bookingId': null,
                    'timeSlots.$.referenceCode': null,
                    'timeSlots.$.timeSlotId': null,
                    'timeSlots.$.bookingDate': null,
                    'timeSlots.$.bookedStaffId': null,
                    'timeSlots.$.bookingStatus': 'available',
                },
            }
        );

        if (result.modifiedCount === 0) {
            return {
                success: false,
                message: 'Time slot not found or already reset.',
            };
        }

        return { success: true, message: 'Time slot reset successfully.' };
    } catch (error) {
        logger.error(`Error resetting time slot: ${error.message}`);
        throw new Error(`Failed to reset time slot: ${error.message}`);
    }
}

//
// 📦 Export
//

module.exports = {
    checkAvailability,
    bookTimeSlot,
    getAvailableTimeSlots,
    deleteBookingTimeSlots,
    getAvailableStaff,
    checkAvailabilityDateRange,
    getAvailableTimeSlotsWithStaff,
};
