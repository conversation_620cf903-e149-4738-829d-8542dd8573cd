const Discount = require('../model/discountModel');

const Counter = require('../counter/discountCounter');

const logger = require('../../../common/utils/logger');

const { validatePromoCode } = require('../../../common/utils/promoValidator');

const ServiceInfo = require('../../serviceInfo/model/serviceInfoModel');

// Create a new discount
const createDiscount = async (discountData, createdBy, status = true) => {
    try {
        logger.info('Creating discount with data:', discountData, createdBy);
        const seq = await Counter.getNextSequence();
        const paddedSeq = seq.toString().padStart(4, '0');
        const discountId = `DCID_${paddedSeq}`;

        const discount = new Discount({
            ...discountData,
            discountId,
            createdBy,
        });
        if (status) {
            await updateServiceDisId(discountData.serviceId, discountId);
        }
        return await discount.save();
    } catch (error) {
        logger.error('Error creating discount:', error);
        throw new Error('Error creating discount: ' + error.message);
    }
};

const updateServiceDisId = async (serviceId, newDiscountId) => {
    try {
        const serviceData = await ServiceInfo.findOne({ serviceId });

        if (!serviceData) {
            throw new Error(`Service with ID ${serviceId} not found`);
        }

        if (serviceData.discountId) {
            await deleteDiscount(serviceData.discountId);
        }

        return await ServiceInfo.findOneAndUpdate(
            { serviceId },
            { $set: { discountId: newDiscountId } },
            { new: true }
        );
    } catch (error) {
        logger.error('Error updating discount:', error.message);
        throw error;
    }
};

// Get multiple discounts with pagination and optional query
const getDiscounts = async (
    query,
    sortBy = 'updatedAt',
    sortDirection = -1,
    pageNum = 1,
    limitNum = 10
) => {
    const skip = (pageNum - 1) * limitNum;

    if (![1, -1].includes(sortDirection)) {
        throw new Error('Invalid sort direction. Use 1 or -1.');
    }

    const now = new Date();

    const enhancedQuery = {
        ...query,
        maxCount: { $gte: 1 },
        'duration.end': { $gt: now }, // ✅ Correct path
    };

    try {
        const discounts = await Discount.find(enhancedQuery)
            .sort({ [sortBy]: sortDirection })
            .skip(skip)
            .limit(limitNum);

        const total = await Discount.countDocuments(enhancedQuery);

        return { items: discounts, total };
    } catch (error) {
        logger.error('Error fetching discounts:', error);
        throw new Error('Failed to retrieve discounts.');
    }
};

// Get a single discount by ID
const getDiscountById = async (discountId) => {
    try {
        return await Discount.findOne({ discountId });
    } catch (error) {
        logger.error('Error fetching discount by ID:', error);
        throw new Error('Failed to retrieve discount.');
    }
};

// Update a discount
const updateDiscount = async (discountId, updateData) => {
    try {
        if (!discountId) throw new Error('Discount ID is required');

        const existing = await getDiscountById(discountId);
        if (!existing) throw new Error('Discount not found');

        return await Discount.findOneAndUpdate(
            { discountId },
            { $set: updateData },
            { new: true, runValidators: true }
        );
    } catch (error) {
        logger.error('Error updating discount:', error.message);
        throw error;
    }
};

// Delete a discount
const deleteDiscount = async (discountId) => {
    try {
        if (!discountId) throw new Error('Discount ID is required');

        const existing = await getDiscountById(discountId);
        if (!existing) throw new Error('Discount not found');

        const deleted = await Discount.findOneAndDelete({ discountId });
        if (!deleted) throw new Error('Discount not found');

        return deleted;
    } catch (error) {
        logger.error('Error deleting discount:', error.message);
        throw error;
    }
};

const checkPromoCodeExists = async (discountId, promoCode) => {
    try {
        const discountData = await getDiscountById(discountId);

        if (!discountData) {
            throw new Error(`Discount with ID "${discountId}" not found`);
        }

        const validCode = discountData.promoCode;
        const { start: startDate, end: endDate } = discountData.duration || {};

        if (!validCode) {
            throw new Error('No promo code associated with this discount');
        }

        const result = validatePromoCode(
            promoCode,
            validCode,
            startDate,
            endDate
        );

        if (!result.valid) {
            throw new Error(result.reason || 'Invalid promo code');
        }

        return result;
    } catch (error) {
        logger.error('Error in checkPromoCodeExists:', {
            message: error.message,
            stack: error.stack,
        });

        throw new Error(error.message);
    }
};

module.exports = {
    createDiscount,
    getDiscounts,
    getDiscountById,
    updateDiscount,
    deleteDiscount,
    checkPromoCodeExists,
};
