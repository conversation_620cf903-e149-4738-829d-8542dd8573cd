const {
    generateImageUrl,
    generateImageUrls,
    generateOptimizedImageUrl,
    getImageConfig,
    CDN_URL
} = require('../services/s3Service');
const logger = require('../utils/logger');
const errorUtil = require('../utils/error');

/**
 * Get image configuration for frontend
 * Returns CDN URL, supported formats, and other image settings
 */
const getImageConfiguration = (req, res) => {
    try {
        const config = getImageConfig();
        
        return res.status(200).json({
            success: true,
            message: 'Image configuration retrieved successfully',
            config: config
        });
    } catch (error) {
        logger.error(`Error getting image configuration: ${error.message}`);
        return errorUtil.handleError(res, error);
    }
};

/**
 * Generate image URL from image path/name
 * Supports both single image and multiple images
 */
const generateImageURL = (req, res) => {
    try {
        const { imageName, imageNames, optimize } = req.query;
        
        if (!imageName && !imageNames) {
            return res.status(400).json({
                success: false,
                message: 'Either imageName or imageNames parameter is required'
            });
        }

        let result;

        if (imageName) {
            // Single image
            if (optimize === 'true') {
                const { width, height, quality, format } = req.query;
                const options = {};
                if (width) options.width = width;
                if (height) options.height = height;
                if (quality) options.quality = quality;
                if (format) options.format = format;
                
                result = {
                    imageName,
                    fullUrl: generateOptimizedImageUrl(imageName, options),
                    cdnUrl: CDN_URL,
                    optimized: true,
                    options
                };
            } else {
                result = {
                    imageName,
                    fullUrl: generateImageUrl(imageName),
                    cdnUrl: CDN_URL,
                    optimized: false
                };
            }
        } else {
            // Multiple images
            const imageNameArray = Array.isArray(imageNames) 
                ? imageNames 
                : imageNames.split(',').map(name => name.trim());
            
            result = {
                images: generateImageUrls(imageNameArray),
                cdnUrl: CDN_URL,
                count: imageNameArray.length
            };
        }

        return res.status(200).json({
            success: true,
            message: 'Image URLs generated successfully',
            data: result
        });
    } catch (error) {
        logger.error(`Error generating image URL: ${error.message}`);
        return errorUtil.handleError(res, error);
    }
};

/**
 * Display image with optional transformations
 * This endpoint can be used to serve images directly with CDN fallback
 */
const displayImage = (req, res) => {
    try {
        const { imageName } = req.params;
        const { width, height, quality, format, redirect = 'true' } = req.query;
        
        if (!imageName) {
            return res.status(400).json({
                success: false,
                message: 'Image name is required'
            });
        }

        // Generate optimized URL if transformations are requested
        const options = {};
        if (width) options.width = width;
        if (height) options.height = height;
        if (quality) options.quality = quality;
        if (format) options.format = format;
        
        const imageUrl = Object.keys(options).length > 0 
            ? generateOptimizedImageUrl(imageName, options)
            : generateImageUrl(imageName);

        if (redirect === 'true') {
            // Redirect to the actual image URL
            return res.redirect(302, imageUrl);
        } else {
            // Return the URL in JSON format
            return res.status(200).json({
                success: true,
                message: 'Image URL generated successfully',
                data: {
                    imageName,
                    fullUrl: imageUrl,
                    cdnUrl: CDN_URL,
                    transformations: options
                }
            });
        }
    } catch (error) {
        logger.error(`Error displaying image: ${error.message}`);
        return errorUtil.handleError(res, error);
    }
};

/**
 * Get multiple image URLs for gallery display
 * Optimized for frontend gallery components
 */
const getGalleryImages = (req, res) => {
    try {
        const { imageNames, thumbnailWidth, thumbnailHeight, fullWidth, fullHeight } = req.body;
        
        if (!imageNames || !Array.isArray(imageNames)) {
            return res.status(400).json({
                success: false,
                message: 'imageNames array is required'
            });
        }

        const galleryImages = imageNames.map(imageName => {
            const baseUrl = generateImageUrl(imageName);
            
            const imageData = {
                imageName,
                fullUrl: baseUrl,
                original: baseUrl
            };

            // Generate thumbnail URL if dimensions provided
            if (thumbnailWidth || thumbnailHeight) {
                const thumbOptions = {};
                if (thumbnailWidth) thumbOptions.width = thumbnailWidth;
                if (thumbnailHeight) thumbOptions.height = thumbnailHeight;
                thumbOptions.quality = '80'; // Default quality for thumbnails
                
                imageData.thumbnail = generateOptimizedImageUrl(imageName, thumbOptions);
            }

            // Generate full-size URL if dimensions provided
            if (fullWidth || fullHeight) {
                const fullOptions = {};
                if (fullWidth) fullOptions.width = fullWidth;
                if (fullHeight) fullOptions.height = fullHeight;
                fullOptions.quality = '90'; // Higher quality for full images
                
                imageData.fullSize = generateOptimizedImageUrl(imageName, fullOptions);
            }

            return imageData;
        });

        return res.status(200).json({
            success: true,
            message: 'Gallery images generated successfully',
            data: {
                images: galleryImages,
                count: galleryImages.length,
                cdnUrl: CDN_URL,
                config: {
                    thumbnailDimensions: {
                        width: thumbnailWidth,
                        height: thumbnailHeight
                    },
                    fullSizeDimensions: {
                        width: fullWidth,
                        height: fullHeight
                    }
                }
            }
        });
    } catch (error) {
        logger.error(`Error getting gallery images: ${error.message}`);
        return errorUtil.handleError(res, error);
    }
};

module.exports = {
    getImageConfiguration,
    generateImageURL,
    displayImage,
    getGalleryImages
};
