/* eslint-disable camelcase */
const { validationResult } = require('express-validator');

const { InsufficientScopeError } = require('express-oauth2-jwt-bearer');

const ServiceInformation = require('./service/serviceInfoService');

const {
    buildQueryFilter,
    validateAndParseParams,
} = require('../../common/utils/filter');

const locationModel = require('./model/locationModel');

const errorUtil = require('../../common/utils/error');

const logger = require('../../common/utils/logger');

const createServiceInformation = async (req, res) => {
    console.log('create Service Info', req.body);

    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            const formattedErrors = errors.array().map((err) => ({
                field: err.path,
                message: err.msg,
            }));

            const errorId = errorUtil.generateErrorId();
            const errorResponse = errorUtil.createErrorResponse(
                formattedErrors,
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                422,
                errorId
            );
            logger.error(`Validation error for request: ${formattedErrors}`, {
                errorId,
                formattedErrors,
            });
            return res.status(422).json(errorResponse);
        }

        const providerId = req.userData.user.userId;
        const providerName = req.userData.user.name;
        const providerEmail = req.userData.user.email;
        const providerPhoneNumber = req.userData.user.phoneNumber;

        const serviceInfo = await ServiceInformation.createServiceInformation(
            req.body,
            providerId,
            providerName,
            providerEmail,
            providerPhoneNumber
        );

        logger.info(
            `ServiceInformation created with ID: ${serviceInfo.serviceId}`
        );

        return res.status(201).json({
            success: true,
            message: 'Service information created successfully',
            serviceInfo,
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();

        if (err instanceof InsufficientScopeError) {
            logger.error(
                `ServiceInfo Insufficient scope error. Message: ${err.message}`,
                { errorId }
            );

            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'ServiceInfo scope', message: err.message }],
                errorUtil.ERROR_TYPES.FORBIDDEN_ERROR,
                403,
                errorId
            );
            logger.error(
                `Insufficient scope error for ServiceInfo. Message: ${err.message}`,
                { errorId }
            );

            return res.status(403).json(errorResponse);
        }

        const errorResponse = errorUtil.createErrorResponse(
            [{ field: 'service', message: err.message }],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        logger.error(`Error creating ServiceInfo. Message: ${err.message}`, {
            errorId,
        });

        return res.status(500).json(errorResponse);
    }
};

// const getServiceInformation = async (req, res) => {
//     const routeType = req.routeType;

//     try {
//         const {
//             page = '1',
//             limit = '10',
//             search,
//             sortBy = 'updatedAt',
//             sortOrder = 'desc',
//         } = req.query;

//         const { pageNum, limitNum, sortDirection } = validateAndParseParams(
//             page,
//             limit,
//             sortOrder
//         );

//         let ProviderId = req.query.ProviderId;
//         if (routeType === 'provider' && req.params.userId) {
//             ProviderId = req.params.userId;
//         } else if (routeType === 'provider') {
//             ProviderId = req.userData?.user?.userId;
//         }

//         const query = buildQueryFilter(search, ProviderId);

//         logger.info(
//             `Fetching service information with params - page: ${page}, limit: ${limit}, search: ${search}, ProviderId: ${ProviderId}, sortBy: ${sortBy}, sortOrder: ${sortOrder}`
//         );

//         const { services, total } =
//             await ServiceInformation.getServiceInformation(
//                 query,
//                 sortBy,
//                 sortDirection,
//                 pageNum,
//                 limitNum
//             );

//         if (!services || services.length === 0) {
//             const errorId = errorUtil.generateErrorId();
//             logger.warn(
//                 `ErrorId:- ${errorId} No services found for the given parameters `
//             );
//             const errorResponse = errorUtil.createErrorResponse(
//                 [],
//                 errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
//                 404,
//                 errorId
//             );

//             return res.status(404).json(errorResponse);
//         }

//         const totalPages = limitNum > 0 ? Math.ceil(total / limitNum) : 0;

//         logger.info(
//             `Fetched ${services.length} ServiceInformations from page ${pageNum}`
//         );

//         return res.status(200).json({
//             success: true,
//             message: 'Service information retrieved successfully',
//             services,
//             total,
//             page: pageNum,
//             pages: totalPages,
//         });
//     } catch (error) {
//         const errorId = errorUtil.generateErrorId();

//         let status = 400;
//         let type = errorUtil.ERROR_TYPES.BAD_REQUEST_ERROR;

//         if (error.message === 'Service temporarily unavailable') {
//             status = 503;
//             type = errorUtil.ERROR_TYPES.SERVICE_UNAVAILABLE_ERROR;
//         } else if (error.name === 'UnauthorizedError') {
//             status = 401;
//             type = errorUtil.ERROR_TYPES.UNAUTHORIZED_ERROR;
//         } else if (error.message === 'Too many requests') {
//             status = 429;
//             type = errorUtil.ERROR_TYPES.TOO_MANY_REQUESTS_ERROR;
//         }

//         logger.error(`Error fetching services: ${error.message}`, {
//             errorId,
//         });

//         const errorResponse = errorUtil.createErrorResponse(
//             [],
//             type,
//             status,
//             errorId
//         );
//         return res.status(status).json(errorResponse);
//     }
// };

const getServiceInformation = async (req, res) => {
    const routeType = req.routeType;

    try {
        const {
            page = '1',
            limit = '10',
            search,
            sortBy = 'updatedAt',
            sortOrder = 'desc',
        } = req.query;

        const { pageNum, limitNum, sortDirection } = validateAndParseParams(
            page,
            limit,
            sortOrder
        );

        let ProviderId;

        if (routeType === 'provider') {
            if (req.params?.userId) {
                ProviderId = req.params.userId;
            } else if (req.userData?.user?.userId) {
                ProviderId = req.userData.user.userId;
            } else if (req.query?.ProviderId) {
                ProviderId = req.query.ProviderId;
            } else {
                const errorId = errorUtil.generateErrorId();
                logger.warn(`ErrorId: ${errorId} Missing ProviderId`);
                return res
                    .status(400)
                    .json(
                        errorUtil.createErrorResponse(
                            [],
                            errorUtil.ERROR_TYPES.BAD_REQUEST_ERROR,
                            400,
                            errorId,
                            'ProviderId is required for provider routes.'
                        )
                    );
            }
        }

        const query = buildQueryFilter(search, ProviderId);

        logger.info('Fetching service information with params', {
            page,
            limit,
            search,
            ProviderId,
            sortBy,
            sortOrder,
        });

        const { services, total } =
            await ServiceInformation.getServiceInformation(
                query,
                sortBy,
                sortDirection,
                pageNum,
                limitNum
            );

        if (!services || services.length === 0) {
            const errorId = errorUtil.generateErrorId();
            logger.warn(
                `ErrorId:- ${errorId} No services found for the given parameters`
            );
            const errorResponse = errorUtil.createErrorResponse(
                [],
                errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                404,
                errorId
            );
            return res.status(404).json(errorResponse);
        }

        const totalPages = limitNum > 0 ? Math.ceil(total / limitNum) : 0;

        logger.info(
            `Fetched ${services.length} ServiceInformations from page ${pageNum}`
        );

        return res.status(200).json({
            success: true,
            message: 'Service information retrieved successfully',
            services,
            total,
            page: pageNum,
            pages: totalPages,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();

        let status = 400;
        let type = errorUtil.ERROR_TYPES.BAD_REQUEST_ERROR;

        if (error.message === 'Service temporarily unavailable') {
            status = 503;
            type = errorUtil.ERROR_TYPES.SERVICE_UNAVAILABLE_ERROR;
        } else if (error.name === 'UnauthorizedError') {
            status = 401;
            type = errorUtil.ERROR_TYPES.UNAUTHORIZED_ERROR;
        } else if (error.message === 'Too many requests') {
            status = 429;
            type = errorUtil.ERROR_TYPES.TOO_MANY_REQUESTS_ERROR;
        }

        logger.error(`Error fetching services: ${error.message}`, { errorId });

        const errorResponse = errorUtil.createErrorResponse(
            [],
            type,
            status,
            errorId
        );
        return res.status(status).json(errorResponse);
    }
};

const getServiceInformationById = async (req, res) => {
    try {
        const { serviceId } = req.params;

        if (!serviceId) {
            const errorId = errorUtil.generateErrorId();
            logger.warn('Service ID parameter is missing', { errorId });
            const errorResponse = errorUtil.createErrorResponse(
                [],
                errorUtil.ERROR_TYPES.BAD_REQUEST_ERROR,
                400,
                errorId
            );
            return res.status(400).json(errorResponse);
        }

        const serviceInfo =
            await ServiceInformation.getServiceInformationById(serviceId);

        if (!serviceInfo) {
            const errorId = errorUtil.generateErrorId();
            logger.warn(`Service information with ID: ${serviceId} not found`, {
                errorId,
            });
            const errorResponse = errorUtil.createErrorResponse(
                [
                    {
                        field: 'serviceId',
                        message: 'Service information not found',
                    },
                ],
                errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                404,
                errorId
            );
            return res.status(404).json(errorResponse);
        }

        logger.info(`Fetched service information with ID: ${serviceId}`);
        return res.status(200).json({
            success: true,
            message: 'Service information fetched successfully',
            serviceInfo,
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();

        if (err.message === 'Service temporarily unavailable') {
            logger.error(`Service unavailable: ${err.message}`, { errorId });
            const errorResponse = errorUtil.createErrorResponse(
                [],
                errorUtil.ERROR_TYPES.SERVICE_UNAVAILABLE_ERROR,
                503,
                errorId
            );
            return res.status(503).json(errorResponse);
        }

        if (err.name === 'UnauthorizedError') {
            logger.error(`Unauthorized access: ${err.message}`, { errorId });
            const errorResponse = errorUtil.createErrorResponse(
                [],
                errorUtil.ERROR_TYPES.UNAUTHORIZED_ERROR,
                401,
                errorId
            );
            return res.status(401).json(errorResponse);
        }

        if (err.message === 'Too many requests') {
            logger.error('Too many requests made in a short period', {
                errorId,
            });
            const errorResponse = errorUtil.createErrorResponse(
                [],
                errorUtil.ERROR_TYPES.TOO_MANY_REQUESTS_ERROR,
                429,
                errorId
            );
            return res.status(429).json(errorResponse);
        }

        logger.error(
            `Error fetching service information with ID ${req.params.serviceId}: ${err.message}`,
            { errorId }
        );

        if (
            err.message.includes('not found') ||
            err.message.includes('ServiceInformation')
        ) {
            const errorResponse = errorUtil.createErrorResponse(
                [
                    {
                        field: 'serviceId',
                        message: 'Service information not found',
                    },
                ],
                errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                404,
                errorId
            );
            return res.status(404).json(errorResponse);
        }

        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

const getAllServiceInformation = async (req, res) => {
    logger.info('getAllServiceInformation Controller:', req.query);
    try {
        const errors = validationResult(req);

        if (!errors.isEmpty()) {
            return res.status(422).json({ errors: errors.array() });
        }

        if (req.query.state === 'all') {
            req.query.state = '';
        }
        if (req.query.categories === 'all') {
            req.query.categories = '';
        }
        if (
            req.query.subCategory?.startsWith('all-') ||
            req.query.subCategory === 'all'
        ) {
            req.query.subCategory = '';
        }
        logger.info('req.query', req.query);
        const {
            page = '1',
            limit = '10',
            search,
            state,
            keyword,
            subCategory,
            categories,
            sortBy = 'updatedAt',
            sortOrder = 'asc',
            latitude,
            longitude,
            radius = '50',
        } = req.query;

        const { pageNum, limitNum, sortDirection } = validateAndParseParams(
            page,
            limit,
            sortOrder
        );

        const query = {
            query: {
                bool: {
                    must: [
                        search
                            ? {
                                  multi_match: {
                                      query: search,
                                      fields: [
                                          'serviceTitle',
                                          'serviceOverview',
                                      ],
                                      fuzziness: 'AUTO',
                                  },
                              }
                            : undefined,

                        // Add a condition for the 'state' if provided
                        // state
                        //     ? {
                        //           term: {
                        //               'locationId.keyword': state,
                        //           },
                        //       }
                        //     : undefined,

                        // Add a condition for the 'keyword' if provided
                        keyword
                            ? {
                                  multi_match: {
                                      query: keyword,
                                      fields: ['seo'], // You can adjust the fields as needed
                                      fuzziness: 'AUTO',
                                  },
                              }
                            : undefined,

                        // Add conditions for categories and subCategories if provided
                        categories
                            ? {
                                  terms: {
                                      categoryId: categories.split(','),
                                  },
                              }
                            : undefined,

                        subCategory
                            ? {
                                  term: {
                                      subCategoryId: subCategory,
                                  },
                              }
                            : undefined,

                        // Add condition for location if provided
                        state
                            ? {
                                  match: {
                                      'location.city': state,
                                  },
                              }
                            : undefined,

                        // Add condition for priceRange if provided
                        // req.query.priceRange
                        //     ? {
                        //         range: {
                        //             price: {
                        //                 gte: req.query.priceRange.split(
                        //                     ','
                        //                 )[0], // Assuming priceRange is a comma-separated range
                        //                 lte: req.query.priceRange.split(
                        //                     ','
                        //                 )[1],
                        //             },
                        //         },
                        //     }
                        //     : undefined,

                        // Add condition for ratings if provided
                        // req.query.ratings
                        //     ? {
                        //           range: {
                        //               ratings: {
                        //                   gte: req.query.ratings, // Assuming ratings is a numeric threshold
                        //               },
                        //           },
                        //       }
                        //     : undefined,
                        {
                            term: {
                                isActive: true, // Filter by active status
                            },
                        },
                    ].filter(Boolean), // Filter out undefined values
                },
            },
            track_total_hits: true,
            from: (pageNum - 1) * limitNum,
            size: limitNum,
        };

        if (latitude && longitude) {
            const lat = parseFloat(latitude);
            const lon = parseFloat(longitude);

            if (!isNaN(lat) && !isNaN(lon)) {
                // Use the new geo_point field: location.coordinates
                query.query.bool.filter = {
                    geo_distance: {
                        distance: `${radius}km`,
                        'location.coordinates': {
                            lat: lat,
                            lon: lon,
                        },
                    },
                };

                query.sort = [
                    {
                        _geo_distance: {
                            'location.coordinates': {
                                lat: lat,
                                lon: lon,
                            },
                            order: 'asc',
                            unit: 'km',
                        },
                    },
                ];
            }
        }

        const { sortedResults, totalRecords } =
            await ServiceInformation.getAllServiceInformation(
                query,
                sortBy,
                sortDirection,
                pageNum,
                limitNum
            );
        const totalPages =
            limitNum > 0 ? Math.ceil(totalRecords / limitNum) : 0;

        // await createAuditLog(req, 'visit');

        return res.status(200).json({
            success: true,
            sortedResults,
            totalRecords,
            page: pageNum,
            pages: totalPages,
        });
    } catch (error) {
        console.error('getProducts Controller:', error);
        return res.status(400).json({ error: error.message });
    }
};

const getCountryStates = async (req, res) => {
    try {
        const errors = validationResult(req);

        if (!errors.isEmpty()) {
            return res.status(422).json({ errors: errors.array() });
        }

        // Get the country from the request query
        const { country } = req.query;

        if (!country) {
            return res.status(400).json({ error: 'Country is required' });
        }

        // Use aggregation to get the unique states for the given country
        const states = await locationModel.aggregate([
            { $match: { country: country } }, // Filter by country
            { $group: { _id: '$state' } }, // Group by state (removes duplicates)
            { $project: { _id: 0, state: '$_id' } }, // Reshape the output
        ]);

        // If no states are found
        if (states.length === 0) {
            return res
                .status(404)
                .json({ message: 'No states found for this country' });
        }

        // Return the list of states
        return res
            .status(200)
            .json({ states: states.map((state) => state.state) });
    } catch (error) {
        console.error('getCountryStates Controller:', error);
        return res.status(400).json({ error: error.message });
    }
};

const updateServiceInformation = async (req, res) => {
    const errorId = errorUtil.generateErrorId();
    console.log('update Service Info', req.boy);

    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            const formattedErrors = errors.array().map((err) => ({
                field: err.path,
                message: err.msg,
            }));

            logger.error('Validation error in updateServiceInformation', {
                errorId,
                formattedErrors,
            });

            return res
                .status(422)
                .json(
                    errorUtil.createErrorResponse(
                        formattedErrors,
                        errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                        422,
                        errorId
                    )
                );
        }

        const { serviceId } = req.params;
        if (!serviceId) {
            logger.warn('Missing serviceId in params', { errorId });
            return res
                .status(400)
                .json(
                    errorUtil.createErrorResponse(
                        [],
                        errorUtil.ERROR_TYPES.BAD_REQUEST_ERROR,
                        400,
                        errorId
                    )
                );
        }

        const existingService =
            await ServiceInformation.getServiceInformationById(serviceId);

        if (!existingService) {
            logger.warn(`Service not found for update: ${serviceId}`, {
                errorId,
            });
            return res.status(404).json(
                errorUtil.createErrorResponse(
                    [
                        {
                            field: 'serviceId',
                            message: 'Service information not found',
                        },
                    ],
                    errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                    404,
                    errorId
                )
            );
        }

        const { role, userId: currentUserId } = req.userData.user;

        const isAdmin = role === 'admin';
        const isOwner = existingService.providerId === currentUserId;

        if (!isAdmin && !isOwner) {
            logger.warn('Permission denied for service update', {
                errorId,
                serviceId,
                role,
                currentUserId,
            });

            return res.status(403).json(
                errorUtil.createErrorResponse(
                    [
                        {
                            field: 'permissions',
                            message: 'Access denied to update this service',
                        },
                    ],
                    errorUtil.ERROR_TYPES.FORBIDDEN_ERROR,
                    403,
                    errorId
                )
            );
        }

        const updatedService =
            await ServiceInformation.updateServiceInformation(
                serviceId,
                req.body
            );

        if (!updatedService) {
            logger.error('Conflict while updating service', {
                errorId,
                serviceId,
            });
            return res
                .status(409)
                .json(
                    errorUtil.createErrorResponse(
                        [],
                        errorUtil.ERROR_TYPES.CONFLICT_ERROR,
                        409,
                        errorId
                    )
                );
        }

        logger.info(`Service updated successfully`, { serviceId });
        return res.status(200).json({
            success: true,
            message: 'Service information updated successfully',
            serviceInfo: updatedService,
        });
    } catch (err) {
        logger.error('Unexpected error in updateServiceInformation', {
            errorId,
            serviceId: req.params.serviceId,
            error: err.message,
        });

        if (err.message === 'Service temporarily unavailable') {
            return res
                .status(503)
                .json(
                    errorUtil.createErrorResponse(
                        [],
                        errorUtil.ERROR_TYPES.SERVICE_UNAVAILABLE_ERROR,
                        503
                    )
                );
        }

        if (err.name === 'UnauthorizedError') {
            return res
                .status(401)
                .json(
                    errorUtil.createErrorResponse(
                        [],
                        errorUtil.ERROR_TYPES.UNAUTHORIZED_ERROR,
                        401
                    )
                );
        }

        if (err.message.includes('not found')) {
            return res.status(404).json(
                errorUtil.createErrorResponse(
                    [
                        {
                            field: 'serviceId',
                            message: 'Service information not found',
                        },
                    ],
                    errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                    404,
                    errorId
                )
            );
        }

        return res
            .status(500)
            .json(
                errorUtil.createErrorResponse(
                    [],
                    errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
                    500,
                    errorId
                )
            );
    }
};

const deleteServiceInformation = async (req, res) => {
    const errorId = errorUtil.generateErrorId();

    try {
        const { serviceId } = req.params;

        if (!serviceId) {
            logger.warn('Service ID parameter is missing', { errorId });
            return res
                .status(400)
                .json(
                    errorUtil.createErrorResponse(
                        [],
                        errorUtil.ERROR_TYPES.BAD_REQUEST_ERROR,
                        400,
                        errorId
                    )
                );
        }

        const serviceInfo =
            await ServiceInformation.getServiceInformationById(serviceId);

        if (!serviceInfo) {
            logger.warn(`Service information not found: ${serviceId}`, {
                errorId,
            });
            return res.status(404).json(
                errorUtil.createErrorResponse(
                    [
                        {
                            field: 'serviceId',
                            message: 'Service information not found',
                        },
                    ],
                    errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                    404,
                    errorId
                )
            );
        }

        const { role, userId: currentUserId } = req.userData.user;

        const isAdmin = role === 'admin';
        const isOwner = serviceInfo.providerId === currentUserId;

        if (!isAdmin && !isOwner) {
            logger.warn('Permission denied for service deletion', {
                errorId,
                serviceId,
                role,
                currentUserId,
            });

            return res.status(403).json(
                errorUtil.createErrorResponse(
                    [
                        {
                            field: 'permissions',
                            message: 'Access denied to delete this service',
                        },
                    ],
                    errorUtil.ERROR_TYPES.FORBIDDEN_ERROR,
                    403,
                    errorId
                )
            );
        }

        const deletionResult =
            await ServiceInformation.deleteServiceInformation(serviceId);

        if (!deletionResult) {
            logger.error('Conflict deleting service', { errorId, serviceId });
            return res
                .status(409)
                .json(
                    errorUtil.createErrorResponse(
                        [],
                        errorUtil.ERROR_TYPES.CONFLICT_ERROR,
                        409,
                        errorId
                    )
                );
        }

        logger.info(
            `ServiceInformation with ID ${serviceId} deleted successfully`
        );
        return res.status(200).json({
            success: true,
            message: 'Service information deleted successfully',
        });
    } catch (err) {
        logger.error(`Error deleting service information`, {
            errorId,
            serviceId: req.params.serviceId,
            error: err.message,
        });

        if (err.message === 'Service temporarily unavailable') {
            return res
                .status(503)
                .json(
                    errorUtil.createErrorResponse(
                        [],
                        errorUtil.ERROR_TYPES.SERVICE_UNAVAILABLE_ERROR,
                        503,
                        errorId
                    )
                );
        }

        if (err.name === 'UnauthorizedError') {
            return res
                .status(401)
                .json(
                    errorUtil.createErrorResponse(
                        [],
                        errorUtil.ERROR_TYPES.UNAUTHORIZED_ERROR,
                        401,
                        errorId
                    )
                );
        }

        if (
            err.message.includes('not found') ||
            err.message.includes('ServiceInformation')
        ) {
            return res.status(404).json(
                errorUtil.createErrorResponse(
                    [
                        {
                            field: 'serviceId',
                            message: 'Service information not found',
                        },
                    ],
                    errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                    404,
                    errorId
                )
            );
        }

        return res
            .status(500)
            .json(
                errorUtil.createErrorResponse(
                    [],
                    errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
                    500,
                    errorId
                )
            );
    }
};

module.exports = {
    createServiceInformation,
    getServiceInformation,
    getServiceInformationById,
    getAllServiceInformation,
    updateServiceInformation,
    getCountryStates,
    deleteServiceInformation,
};
