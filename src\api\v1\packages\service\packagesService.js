const Package = require('../model/packageModel');

const Counter = require('../counter/packagesCounter');

const logger = require('../../../common/utils/logger');

const DiscountService = require('./discountService');

const ServiceInfo = require('../../serviceInfo/model/serviceInfoModel');

const createPackage = async (packageData, createdBy, status = true) => {
    logger.info('Creating package with data:', packageData, createdBy);
    try {
        const providerId = packageData.providerId;

        const existingDiscount = packageData.discount;

        const pid = await Counter.getNextSequence();

        const paddedSeq = pid.toString().padStart(4, '0');

        const packageId = `PCID_${paddedSeq}`;

        if (existingDiscount) {
            const discount = await DiscountService.createDiscount(
                {
                    ...existingDiscount,
                    providerId,
                    serviceId: packageData.serviceId,
                    serviceName: packageData.serviceName,
                    packageId,
                    packageName: packageData.packageName,
                },
                createdBy,
                false
            );

            packageData.discountId = discount.discountId;
        }

        const newPackage = new Package({
            ...packageData,
            packageId,
        });
        if (status) {
            await updateServicePackageIds(packageData.serviceId, packageId);
        }

        return await newPackage.save();
    } catch (error) {
        logger.error('Error creating package:', error);
        throw new Error('Error creating package: ' + error.message);
    }
};

const updateServicePackageIds = async (serviceId, newPackageIds) => {
    try {
        // Normalize newPackageIds to an array
        let packageIdsToAdd = [];

        if (Array.isArray(newPackageIds)) {
            packageIdsToAdd = newPackageIds;
        } else if (newPackageIds) {
            packageIdsToAdd = [newPackageIds];
        } else {
            throw new Error('No valid package ID(s) provided');
        }

        // Fetch existing service
        const serviceData = await ServiceInfo.findOne({ serviceId });

        if (!serviceData) {
            throw new Error(`Service with ID ${serviceId} not found`);
        }

        const existingPackageIds = Array.isArray(serviceData.packageId)
            ? serviceData.packageId
            : [];

        // Merge and deduplicate
        const mergedPackageIds = [
            ...new Set([...existingPackageIds, ...packageIdsToAdd]),
        ];

        logger.info(
            `Updating service ${serviceId} with packages: ${mergedPackageIds.join(', ')}`
        );

        // Update the service
        const updatedService = await ServiceInfo.findOneAndUpdate(
            { serviceId },
            { $set: { packageId: mergedPackageIds } },
            { new: true, runValidators: true }
        );

        return updatedService;
    } catch (error) {
        logger.error(
            `Error updating packageIds for service ${serviceId}: ${error.message}`
        );
        throw error;
    }
};

const getPackages = async (
    query,
    sortBy = 'updatedAt',
    sortDirection = -1,
    pageNum = 1,
    limitNum = 10
) => {
    const skip = (pageNum - 1) * limitNum;

    if (![1, -1].includes(sortDirection)) {
        throw new Error(
            'Invalid sort direction. Use 1 for ascending and -1 for descending.'
        );
    }

    try {
        // Get base package list
        const basePackages = await Package.find(query)
            .sort({ [sortBy]: sortDirection })
            .skip(skip)
            .limit(limitNum)
            .select('packageId'); // only need packageId for now

        const total = await Package.countDocuments(query);

        // Fetch full package data for each item
        const detailedPackages = await Promise.all(
            basePackages.map((pkg) => getPackageById(pkg.packageId))
        );

        return {
            items: detailedPackages,
            total,
        };
    } catch (error) {
        logger.error('Error fetching packages:', error);
        throw new Error('Failed to retrieve packages.');
    }
};

const getPackageById = async (packageId) => {
    try {
        const packageDoc = await Package.findOne({ packageId }).select(
            '-createdAt -updatedAt -__v'
        );

        if (!packageDoc) {
            throw new Error('Package not found.');
        }

        const packageData = packageDoc.toObject();

        // Fetch discount details if discountId exists
        if (packageData.discountId) {
            const discountDetails = await DiscountService.getDiscountById(
                packageData.discountId
            );
            packageData.discount = discountDetails;

            // Remove discountId to avoid duplication in response
            delete packageData.discountId;
        }

        return packageData;
    } catch (error) {
        logger?.error?.('Error fetching package by ID:', error);
        throw new Error('Failed to retrieve package.');
    }
};

const updatePackageById = async (packageId, updateData) => {
    try {
        if (!packageId) throw new Error('Package ID is required');

        const existingPackage = await getPackageById(packageId);

        if (!existingPackage) throw new Error('Package not found');

        if (updateData.discount && existingPackage.discount.discountId) {
            await DiscountService.updateDiscount(
                existingPackage.discount.discountId,
                updateData.discount
            );

            // Remove discount from updateData to avoid updating it in Package model
            delete updateData.discount;
        }

        const updated = await Package.findOneAndUpdate(
            { packageId },
            { $set: updateData },
            { new: true, runValidators: true }
        );

        return updated;
    } catch (error) {
        logger?.error?.('Error updating package:', error.message);
        throw new Error('Failed to update package.');
    }
};

const deletePackageById = async (packageId) => {
    try {
        if (!packageId) throw new Error('Package ID is required');

        const existingPackage = await getPackageById(packageId);
        if (!existingPackage) throw new Error('Package not found');

        // Delete discount only if discountId exists
        if (existingPackage.discount.discountId) {
            await DiscountService.deleteDiscount(
                existingPackage.discount.discountId
            );
        }

        const deletedPackage = await Package.findOneAndDelete({ packageId });
        if (!deletedPackage) throw new Error('Package not found');

        return deletedPackage;
    } catch (error) {
        logger.error('Error deleting package:', error.message);
        throw error;
    }
};

module.exports = {
    createPackage,
    getPackages,
    getPackageById,
    updatePackageById,
    deletePackageById,
};
