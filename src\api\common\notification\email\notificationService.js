const dotenv = require('dotenv');

const { sendEmail } = require('./template/sendMail');

const RegisterBookingEmailTemplate = require('./template/bookingCreateEmail');

const CreateBookingEmailTemplate = require('./template/createBookingEmail');

const bookingStatusEmail = require('./template/updateStatusEmail');

const DeleteBookingEmailTemplate = require('./template/deleteBookingEmail');

const logger = require('../../../common/utils/logger');

const { getUserById } = require('../../../common/config/communicateDB');

const staffService = require('../../../v1/staff/service/staffService');

const serviceService = require('../../../v1/serviceInfo/service/serviceInfoService');

const docService = require('./docs/docs');

const path = require('path');

const s3Service = require('./docs/uploadFile');

dotenv.config();

function getBookedAdditionalServices(booking, serviceInfo) {
    const bookedIds = booking.additionalServiceIds;
    const allAdditionalServices = serviceInfo.additionalServices;

    const bookedServices = allAdditionalServices.filter((service) =>
        bookedIds.includes(service.id)
    );

    return bookedServices;
}
/**
 * Sends event details email to the specified user.
 * @param {string} email - The recipient's email address.
 */
const sendBookingDetailsEmail = async (
    bookingData,
    customerId,
    referenceCode
) => {
    try {
        const { providerId, staffId, serviceId } = bookingData;

        const customerData = await getUserById(customerId);

        const providerData = await getUserById(providerId);

        const staffData = await staffService.getStaffById(staffId);

        const serviceData =
            await serviceService.getServiceInformationById(serviceId);

        const additionalServices = getBookedAdditionalServices(
            bookingData,
            serviceData
        );

        const { subject, htmlbody } = await RegisterBookingEmailTemplate(
            referenceCode,
            bookingData,
            providerData,
            staffData,
            serviceData,
            additionalServices
        );

        const { newSubject, newHtmlbody } = await CreateBookingEmailTemplate(
            referenceCode,
            bookingData,
            providerData,
            serviceData
        );

        await docService.saveHtml(htmlbody);

        const folderPath = path.join(__dirname, 'docs');

        const htmlPath = path.join(folderPath, 'output.html');

        const pdfPath = path.join(folderPath, 'output.pdf');

        const now = new Date();

        const year = now.getFullYear();

        const monthName = now.toLocaleString('default', { month: 'long' });

        const nestedPath = `${year}/${monthName}`;

        await docService.generatePdfFromHtml(htmlPath, pdfPath);

        const providerMainFolder = `${providerId}-${providerData.user.name}`;

        const customerMainFolder = `${customerId}-${customerData.user.name}`;

        const fileName = `${referenceCode}.pdf`;

        const providerResult = await s3Service.uploadFileToS3FromDisk(pdfPath, {
            baseFolder: 'provider',
            mainFolder: providerMainFolder,
            subFolder: 'invoices',
            nestedPath: nestedPath, // should already be like "2025/August"
            fileName: fileName,
        });

        const customerResult = await s3Service.uploadFileToS3FromDisk(pdfPath, {
            baseFolder: 'customer',
            mainFolder: customerMainFolder,
            subFolder: 'invoices',
            nestedPath: nestedPath,
            fileName: fileName,
        });

        const recipients = [
            {
                email: customerData.user.email,
                subject: newSubject,
                htmlbody: newHtmlbody,
                pdfPath: '',
            },

            {
                email: providerData.user.email,
                subject: newSubject,
                htmlbody: newHtmlbody,
                pdfPath: '',
            },

            {
                email: customerData.user.email,
                subject: subject,
                htmlbody: htmlbody,
                pdfPath: pdfPath,
            },

            {
                email: providerData.user.email,
                subject: subject,
                htmlbody: htmlbody,
                pdfPath: pdfPath,
            },
        ];

        for (const { email, subject, htmlbody, pdfPath } of recipients) {
            await sendEmail(email, subject, htmlbody, pdfPath, referenceCode);
        }

        return {
            success: true,
            message: 'Email sent successfully',
            data: {
                providerUrl: providerResult.url,
                customerUrl: customerResult.url,
            },
        };
    } catch (error) {
        logger.error('Error sending booking details email:', error);
        throw error;
    }
};

const bookingStatusUpdateEmail = async (
    customerId,
    providerId,
    bookingId,
    newStatus
) => {
    try {
        const { subject, htmlbody } = await bookingStatusEmail(
            bookingId,
            newStatus
        );
        const customerData = await getUserById(customerId);

        const providerData = await getUserById(providerId);

        await sendEmail(customerData.user.email, subject, htmlbody);
        await sendEmail(providerData.user.email, subject, htmlbody);
        return { success: true, message: 'Email sent successfully' };
    } catch (error) {
        logger.error('Error sending event details email', error);
        throw error;
    }
};

/**
 * Sends update event emails to all registered users for a specified event.
 * @param {string} eventId - The ID of the event for which updates are sent.
 */

/**
 * Sends a delete event email to the specified user.
 * @param {string} email - The recipient's email address.
 * @param {string} eventId - The ID of the event being deleted.
 */
const sendDeleteBookingEmail = async (
    customerId,
    providerId,
    referenceCode
) => {
    try {
        const { subject, htmlbody } =
            await DeleteBookingEmailTemplate(referenceCode);
        const customerData = await getUserById(customerId);

        const providerData = await getUserById(providerId);

        await sendEmail(customerData.user.email, subject, htmlbody);
        await sendEmail(providerData.user.email, subject, htmlbody);
    } catch (error) {
        logger.error('Error sending delete event email', error);
        throw error;
    }
};

module.exports = {
    sendBookingDetailsEmail,
    sendDeleteBookingEmail,
    bookingStatusUpdateEmail,
};
