const mongoose = require('mongoose');

// Reply Schema (for threaded conversations)
const replySchema = new mongoose.Schema(
    {
        replyId: {
            type: String,
            required: true,
        },
        reviewId: {
            type: String,
            required: true,
        },
        userId: {
            type: String,
            required: true,
        },
        userType: {
            type: String,
            enum: ['customer', 'provider'],
            required: true,
        },
        replyText: {
            type: String,
            required: true,
            trim: true,
            maxlength: 1000,
        },
        replyDate: {
            type: Date,
            default: Date.now,
        },
        isEdited: {
            type: Boolean,
            default: false,
        },
        editedAt: {
            type: Date,
        },
        // Reference to parent reply (for nested replies)
        parentReplyId: {
            type: String,
            default: null,
        },
    },
    { _id: false }
);

// Review Response Schema (for provider responses)
const reviewResponseSchema = new mongoose.Schema(
    {
        responseId: {
            type: String,
            required: true,
        },
        providerId: {
            type: String,
            required: true,
        },
        responseText: {
            type: String,
            required: true,
            trim: true,
            maxlength: 1000,
        },
        responseDate: {
            type: Date,
            default: Date.now,
        },
        isEdited: {
            type: Boolean,
            default: false,
        },
        editedAt: {
            type: Date,
        },
    },
    { _id: false }
);

// Review Images Schema
const reviewImageSchema = new mongoose.Schema(
    {
        imageId: {
            type: String,
            required: true,
        },
        imageUrl: {
            type: String,
            required: true,
        },
        imageCaption: {
            type: String,
            trim: true,
            maxlength: 200,
        },
        uploadedAt: {
            type: Date,
            default: Date.now,
        },
    },
    { _id: false }
);

// Helpful Votes Schema
const helpfulVotesSchema = new mongoose.Schema(
    {
        userId: {
            type: String,
            required: true,
        },
        voteType: {
            type: String,
            enum: ['helpful', 'not_helpful'],
            required: true,
        },
        votedAt: {
            type: Date,
            default: Date.now,
        },
    },
    { _id: false }
);

// Main Review Schema
const reviewSchema = new mongoose.Schema(
    {
        reviewId: {
            type: String,
            required: true,
            unique: true,
        },
        
        // Core Review Data
        customerId: {
            type: String,
            required: true,
        },

        // Reviewer Information (saved from auth service at time of review creation)
        reviewerInfo: {
            name: {
                type: String,
                required: true,
                trim: true,
                maxlength: 100,
            },
            email: {
                type: String,
                trim: true,
                maxlength: 255,
            },
            profilePicture: {
                type: String,
                trim: true,
                maxlength: 500,
            },
            isVerified: {
                type: Boolean,
                default: false,
            },
            userType: {
                type: String,
                enum: ['customer', 'provider', 'admin'],
                default: 'customer',
            },
        },
        providerId: {
            type: String,
            required: true,
        },
        serviceId: {
            type: String,
            required: true,
        },
        bookingId: {
            type: String,
            required: true,
        },
        
        // Review Content
        // Category ratings (1-5 scale)
        serviceRating: {
            type: Number,
            required: true,
            min: 1,
            max: 5,
        },
        qualityRating: {
            type: Number,
            required: true,
            min: 1,
            max: 5,
        },
        timelinessRating: {
            type: Number,
            required: true,
            min: 1,
            max: 5,
        },
        communicationRating: {
            type: Number,
            required: true,
            min: 1,
            max: 5,
        },
        valueRating: {
            type: Number,
            required: true,
            min: 1,
            max: 5,
        },

        // Overall rating (calculated from category ratings)
        overallRating: {
            type: Number,
            min: 1,
            max: 5,
        },

        // Legacy rating field for backward compatibility (1-5 scale)
        rating: {
            type: Number,
            min: 1,
            max: 5,
        },

        title: {
            type: String,
            required: true,
            trim: true,
            maxlength: 100,
        },
        comment: {
            type: String,
            required: true,
            trim: true,
            maxlength: 2000,
        },

        // Review Images - both old and new formats
        images: [reviewImageSchema], // Legacy format
        imageNames: [{
            type: String,
            trim: true,
            maxlength: 255,
        }], // New format - S3 file names
        
        // Review Status
        status: {
            type: String,
            enum: ['pending', 'approved', 'rejected', 'hidden'],
            default: 'approved',
        },
        
        // Moderation
        moderationReason: {
            type: String,
            trim: true,
        },
        moderatedBy: {
            type: String,
        },
        moderatedAt: {
            type: Date,
        },
        
        // Provider Response
        providerResponse: reviewResponseSchema,

        // Review Replies (parallel to review comments and provider response)
        replies: [replySchema],

        // Helpful Votes
        helpfulVotes: [helpfulVotesSchema],
        helpfulCount: {
            type: Number,
            default: 0,
        },
        notHelpfulCount: {
            type: Number,
            default: 0,
        },
        
        // Review Metadata
        isVerifiedPurchase: {
            type: Boolean,
            default: true,
        },
        isEdited: {
            type: Boolean,
            default: false,
        },
        editedAt: {
            type: Date,
        },
        
        // Timestamps
        reviewDate: {
            type: Date,
            default: Date.now,
        },
        
        // Soft Delete
        isDeleted: {
            type: Boolean,
            default: false,
        },
        deletedAt: {
            type: Date,
        },
        deletedBy: {
            type: String,
        },
    },
    { 
        timestamps: true,
        toJSON: { virtuals: true },
        toObject: { virtuals: true }
    }
);

// Indexes for better query performance
reviewSchema.index({ providerId: 1, status: 1, isDeleted: 1 });
reviewSchema.index({ serviceId: 1, status: 1, isDeleted: 1 });
reviewSchema.index({ customerId: 1, isDeleted: 1 });
reviewSchema.index({ bookingId: 1 });
reviewSchema.index({ rating: 1, status: 1, isDeleted: 1 });
reviewSchema.index({ reviewDate: -1 });

// Virtual for overall helpfulness score
reviewSchema.virtual('helpfulnessScore').get(function() {
    const total = this.helpfulCount + this.notHelpfulCount;
    if (total === 0) return 0;
    return (this.helpfulCount / total) * 100;
});

// Virtual for calculated overall rating from category ratings
reviewSchema.virtual('calculatedOverallRating').get(function() {
    if (this.serviceRating && this.qualityRating && this.timelinessRating && this.communicationRating && this.valueRating) {
        const average = (this.serviceRating + this.qualityRating + this.timelinessRating + this.communicationRating + this.valueRating) / 5;
        return Math.round(average * 10) / 10; // Round to 1 decimal place
    }
    return null;
});

// Pre-save middleware to update helpful counts and overall rating
reviewSchema.pre('save', function(next) {
    if (this.isModified('helpfulVotes')) {
        this.helpfulCount = this.helpfulVotes.filter(vote => vote.voteType === 'helpful').length;
        this.notHelpfulCount = this.helpfulVotes.filter(vote => vote.voteType === 'not_helpful').length;
    }

    // Calculate overall rating from category ratings if not provided
    if (this.isModified(['serviceRating', 'qualityRating', 'timelinessRating', 'communicationRating', 'valueRating']) || this.isNew) {
        if (this.serviceRating && this.qualityRating && this.timelinessRating && this.communicationRating && this.valueRating) {
            if (!this.overallRating) {
                const average = (this.serviceRating + this.qualityRating + this.timelinessRating + this.communicationRating + this.valueRating) / 5;
                this.overallRating = Math.round(average * 10) / 10;
            }

            // Also set legacy rating field (both scales are now 1-5, so no conversion needed)
            if (!this.rating) {
                this.rating = this.overallRating;
            }
        }
    }

    next();
});

const Review = mongoose.model('Review', reviewSchema);

module.exports = Review;
