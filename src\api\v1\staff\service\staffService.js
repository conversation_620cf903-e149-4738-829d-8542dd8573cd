const Staff = require('../model/staffModel');

const Counter = require('../counter/staffCounter');

const logger = require('../../../common/utils/logger');

const createStaff = async (staffData, providerId) => {
    try {
        const stid = await Counter.getNextSequence();
        const staffId = `STID_${stid}`;

        const providerStaffId = `${providerId}-${staffId}`;

        const staff = new Staff({
            ...staffData,
            staffId,
            providerStaffId,
            providerId,
        });

        return await staff.save();
    } catch (error) {
        throw new Error('Error creating Staff: ' + error.message);
    }
};

const getStaff = async (query, sortBy, sortDirection, pageNum, limitNum) => {
    const skip = (pageNum - 1) * limitNum;

    if (![1, -1].includes(sortDirection)) {
        throw new Error(
            'Invalid sort direction. Use 1 for ascending and -1 for descending.'
        );
    }

    try {
        const staffList = await Staff.find(query)
            .sort({ [sortBy]: sortDirection })
            .skip(skip)
            .limit(limitNum);

        const total = await Staff.countDocuments(query);

        return { staffList, total };
    } catch (error) {
        console.error('Error fetching staff:', error);
        throw new Error('Failed to retrieve staff.');
    }
};

const getAllStaff = async () => {
    try {
        const staffList = await Staff.find({}).sort({ createdAt: -1 });
        return staffList;
    } catch (error) {
        logger.error('Error fetching all staff:', error);
        throw new Error('Failed to retrieve all staff.');
    }
};

const getStaffById = async (staffId) => {
    return Staff.findOne({ staffId });
};

const updateStaff = async (staffId, updateData) => {
    try {
        if (!staffId) throw new Error('Staff ID is required');

        const staff = await getStaffById(staffId);

        if (!staff) throw new Error('Staff not found');

        const updatedStaff = await Staff.findOneAndUpdate(
            { staffId },
            { $set: updateData },
            { new: true, runValidators: true }
        );

        return updatedStaff;
    } catch (error) {
        logger.error('Error updating Staff:', error.message);
        throw error;
    }
};

async function updateStaffServiceIds(staff, newServiceId) {
    const staffList = Array.isArray(staff) ? staff : [staff];

    for (const personId of staffList) {
        logger.info(`Processing personId: ${personId}`);

        try {
            logger.info(`Fetching staff information for personId: ${personId}`);
            const currentStaff = await getStaffById(personId);
            logger.info(`Fetched staff information`);

            if (Array.isArray(currentStaff.serviceIds)) {
                logger.info(
                    `Adding new serviceId: ${newServiceId} to existing serviceIds`
                );
                currentStaff.serviceIds.push(newServiceId);
            } else {
                logger.warn(
                    `serviceIds is not an array. Initializing it with new serviceId: ${newServiceId}`
                );
                currentStaff.serviceIds = [newServiceId];
            }

            logger.info(`Updating serviceIds for personId: ${personId}`);
            await updateStaff(personId, {
                serviceIds: currentStaff.serviceIds,
            });
            logger.info(
                `Successfully updated personId: ${personId} with new serviceIds`
            );
        } catch (error) {
            logger.error(`Error updating personId: ${personId}`, error);
        }
    }
}

const deleteStaff = async (staffId) => {
    try {
        if (!staffId) throw new Error('Staff ID is required');

        const staff = await getStaffById(staffId);

        if (!staff) throw new Error('Staff not found');

        const deletedStaff = await Staff.findOneAndDelete({ staffId: staffId });

        if (!deletedStaff) {
            throw new Error('Staff not found or already deleted');
        }

        return deletedStaff;
    } catch (error) {
        throw new Error(error.message);
    }
};

async function removeServiceIdFromStaff(staff, serviceIdToRemove) {
    const staffList = Array.isArray(staff) ? staff : [staff];

    for (const personId of staffList) {
        logger.info(`Processing personId: ${personId}`);

        try {
            logger.info(`Fetching staff information for personId: ${personId}`);
            const currentStaff = await getStaffById(personId);
            logger.info(`Fetched staff information: `);

            if (Array.isArray(currentStaff.serviceIds)) {
                logger.info(
                    `Removing serviceId: ${serviceIdToRemove} from serviceIds`
                );
                currentStaff.serviceIds = currentStaff.serviceIds.filter(
                    (serviceId) => serviceId !== serviceIdToRemove
                );
                logger.info(
                    `Updated serviceIds: ${JSON.stringify(currentStaff.serviceIds)}`
                );
            } else {
                logger.warn(
                    `serviceIds is not an array or is empty. Initializing it as an empty array.`
                );
                currentStaff.serviceIds = [];
            }

            logger.info(`Updating serviceIds for personId: ${personId}`);
            await updateStaff(personId, {
                serviceIds: currentStaff.serviceIds,
            });
            logger.info(
                `Successfully updated personId: ${personId} with updated serviceIds: `
            );
        } catch (error) {
            logger.error(`Error updating personId: ${personId}`, error);
        }
    }
}

const incrementCompletedServices = async (staffId) => {
    if (!staffId) {
        throw new Error(
            'Staff ID is required to increment completed services.'
        );
    }

    await Staff.updateOne(
        { staffId: staffId },
        { $inc: { numberOfCompletedServices: 1 } }
    );
};

module.exports = {
    createStaff,
    getStaff,
    getStaffById,
    updateStaff,
    updateStaffServiceIds,
    deleteStaff,
    removeServiceIdFromStaff,
    incrementCompletedServices,
    getAllStaff,
};
