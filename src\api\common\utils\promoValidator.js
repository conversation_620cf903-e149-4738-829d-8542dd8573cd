const CASE_INSENSITIVE = true;

const GENERIC_ERROR_MESSAGE = 'Invalid promo code';

const EXPIRED_ERROR_MESSAGE = 'Promo code is expired or not active yet';

function validatePromoCode(inputCode, validCode, startDate, endDate) {
    if (!inputCode || typeof inputCode !== 'string') {
        return { valid: false, reason: GENERIC_ERROR_MESSAGE };
    }

    const trimmedInput = inputCode.trim();

    // Optional: allow only safe promo characters
    const allowedPattern = /^[A-Za-z0-9@\-!#&$%*]+$/;
    if (!allowedPattern.test(trimmedInput)) {
        return { valid: false, reason: GENERIC_ERROR_MESSAGE };
    }

    const normalizedInput = CASE_INSENSITIVE
        ? trimmedInput.toUpperCase()
        : trimmedInput;

    const normalizedValidCode = CASE_INSENSITIVE
        ? validCode.toUpperCase()
        : validCode;

    if (normalizedInput !== normalizedValidCode) {
        return { valid: false, reason: GENERIC_ERROR_MESSAGE };
    }

    // If both start and end dates are provided, validate the date range
    if (startDate && endDate) {
        const now = new Date();
        const start = new Date(startDate);
        const end = new Date(endDate);

        if (isNaN(start.getTime()) || isNaN(end.getTime())) {
            return { valid: false, reason: 'Invalid start or end date format' };
        }

        if (now < start || now > end) {
            return { valid: false, reason: EXPIRED_ERROR_MESSAGE };
        }
    }

    return { valid: true, code: trimmedInput };
}

module.exports = { validatePromoCode };
