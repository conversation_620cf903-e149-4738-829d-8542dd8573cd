const mongoose = require('mongoose');
const { Schema } = mongoose;

const durationSchema = new Schema({
    start: { type: Date, required: true },

    end: { type: Date },
});

const discountSchema = new Schema(
    {
        discountId: { type: String, required: true, unique: true },

        serviceId: {
            type: String,
        },

        serviceName: { type: String },

        packageId: { type: String },

        packageName: { type: String },

        providerId: { type: String },

        isDiscount: { type: Boolean, default: false },

        discountType: {
            type: String,
            enum: ['promo-code', 'general-discount'],
            required: true,
        },
        valueType: {
            type: String,
            enum: ['amount', 'percentage'],
            required: true,
        },
        durationType: {
            type: String,
            enum: ['life-time', 'time-base'],
            required: true,
        },
        amount: { type: Number, required: true },

        duration: { type: durationSchema, required: true },

        promoCode: { type: String },

        maxCount: { type: Number },

        createdBy: { type: String },

        isPackageDiscount: {
            type: Boolean,
            default: false,
        },

        soldOut: { type: Boolean, default: false },

        IsActive: { type: Boolean, default: true },

        acceptAllUserIds: { type: [String], default: [] },
    },

    { timestamps: true }
);

module.exports = mongoose.model('Discount', discountSchema);
