/* eslint-disable id-length */
const { validationResult } = require('express-validator');

const { InsufficientScopeError } = require('express-oauth2-jwt-bearer');

const PackageService = require('./service/packagesService');

const DiscountService = require('./service/discountService');

const {
    buildQueryFilter,
    validateAndParseParams,
} = require('../../common/utils/filter');

const logger = require('../../common/utils/logger');

const errorUtil = require('../../common/utils/error');

// Controller: createPackagesAndDiscount
const createPackagesAndDiscount = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            const formattedErrors = errors.array().map((err) => ({
                field: err.path,
                message: err.msg,
            }));

            const errorId = errorUtil.generateErrorId();
            const errorResponse = errorUtil.createErrorResponse(
                formattedErrors,
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                422,
                errorId
            );

            logger.error(
                `Validation error for request: ${JSON.stringify(formattedErrors)}`,
                { errorId, formattedErrors }
            );

            return res.status(422).json(errorResponse);
        }

        const userId = req.userData.user.userId;

        // Use appropriate service method based on isDiscount flag
        const serviceMethod = req.body.isDiscount
            ? DiscountService.createDiscount
            : PackageService.createPackage;

        const result = await serviceMethod(req.body, userId);

        logger.info(
            `${req.body.isDiscount ? 'Discount' : 'Package'} created successfully. ID: ${
                req.body.isDiscount ? result.discountId : result.packageId
            }`
        );

        return res.status(201).json({
            success: true,
            message: `${req.body.isDiscount ? 'Discount' : 'Package'} created successfully`,
            package: result, // or consider using "item" instead of "package" for clarity
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();

        if (err instanceof InsufficientScopeError) {
            logger.error(`Insufficient scope error. Message: ${err.message}`, {
                errorId,
            });

            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'scope', message: err.message }],
                errorUtil.ERROR_TYPES.FORBIDDEN_ERROR,
                403,
                errorId
            );

            return res.status(403).json(errorResponse);
        }

        logger.error(`Error creating item. Message: ${err.message}`, {
            errorId,
        });

        const errorResponse = errorUtil.createErrorResponse(
            [{ field: 'item', message: err.message }],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );

        return res.status(500).json(errorResponse);
    }
};

const getPackagesAndDiscount = async (req, res) => {
    try {
        const path = req.path;

        // Better check for isDiscount
        const isDiscount = path.includes('/discounts');

        const {
            page = '1',
            limit = '10',
            search,
            sortBy = 'updatedAt',
            sortOrder = 'desc',
        } = req.query;

        const { pageNum, limitNum, sortDirection } = validateAndParseParams(
            page,
            limit,
            sortOrder
        );

        // Determine providerId filter if routeType is 'provider'
        let providerId = req.query.ProviderId;
        if (req.routeType === 'provider') {
            providerId = req.userData?.user?.userId;
        }
        logger.info(`Provider ID filter: ${providerId}`);

        // Build base query with search filter and isDiscount
        const query = {
            ...buildQueryFilter(search, providerId),
            isDiscount,
        };

        const service = isDiscount
            ? DiscountService.getDiscounts
            : PackageService.getPackages;

        const { items, total } = await service(
            query,
            sortBy,
            sortDirection,
            pageNum,
            limitNum
        );

        const totalPages = limitNum > 0 ? Math.ceil(total / limitNum) : 0;

        return res.status(200).json({
            success: true,
            message: `${isDiscount ? 'Discounts' : 'Packages'} fetched successfully`,
            [isDiscount ? 'discounts' : 'packages']: items,
            total,
            page: pageNum,
            pages: totalPages,
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();
        logger.error(`Error fetching ${req.path}. Message: ${err.message}`, {
            errorId,
        });

        return res
            .status(500)
            .json(
                errorUtil.createErrorResponse(
                    [],
                    errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
                    500,
                    errorId
                )
            );
    }
};

const getPackagesAndDiscounts = async (req, res) => {
    try {
        // Extract and sanitize query parameters
        const {
            page = '1',
            limit = '10',
            search = '',
            sortBy = 'updatedAt',
            sortOrder = 'desc',
            ProviderId,
        } = req.query;

        const { pageNum, limitNum, sortDirection } = validateAndParseParams(
            page,
            limit,
            sortOrder
        );

        // Determine provider ID filter based on routeType
        const providerId =
            req.routeType === 'provider'
                ? req.userData?.user?.userId
                : ProviderId;

        logger.info(
            `Fetching for Provider ID: ${providerId || 'All Providers'}`
        );

        // Base filter for both entities
        const baseQuery = buildQueryFilter(search, providerId);

        // Separate queries for packages and discounts
        const packageQuery = { ...baseQuery, isDiscount: false };
        const discountQuery = { ...baseQuery, isDiscount: true };

        // Fetch both in parallel
        const [packagesResult, discountsResult] = await Promise.all([
            PackageService.getPackages(
                packageQuery,
                sortBy,
                sortDirection,
                pageNum,
                limitNum
            ),
            DiscountService.getDiscounts(
                discountQuery,
                sortBy,
                sortDirection,
                pageNum,
                limitNum
            ),
        ]);

        const buildMeta = ({ total }) => ({
            total,
            pages: limitNum > 0 ? Math.ceil(total / limitNum) : 0,
        });

        return res.status(200).json({
            success: true,
            message: 'Packages and Discounts fetched successfully',
            data: {
                packages: packagesResult.items,
                discounts: discountsResult.items,
            },
            meta: {
                page: pageNum,
                limit: limitNum,
                packages: buildMeta(packagesResult),
                discounts: buildMeta(discountsResult),
            },
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();

        logger.error(`Error fetching packages and discounts: ${err.message}`, {
            errorId,
            stack: err.stack,
        });

        return res
            .status(500)
            .json(
                errorUtil.createErrorResponse(
                    [],
                    errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
                    500,
                    errorId
                )
            );
    }
};

const getPackagesAndDiscountById = async (req, res) => {
    let id, item, errorField, serviceMethod;

    try {
        const pathSegments = req.path.split('/').filter(Boolean);
        const resourceType = pathSegments[0]; // 'packages' or 'discounts'

        if (resourceType === 'packages') {
            id = req.params.productId;
            errorField = 'productId';
            serviceMethod = PackageService.getPackageById;
        } else if (resourceType === 'discounts') {
            id = req.params.discountId;
            errorField = 'discountId';
            serviceMethod = DiscountService.getDiscountById;
        } else {
            return res.status(400).json({
                success: false,
                message: 'Invalid path',
            });
        }

        item = await serviceMethod(id);

        if (!item) {
            const errorId = errorUtil.generateErrorId();
            logger.warn(
                `${resourceType.slice(0, -1)} with ID ${id} not found`,
                {
                    errorId,
                }
            );

            const errorResponse = errorUtil.createErrorResponse(
                [
                    {
                        field: errorField,
                        message: `${resourceType.slice(0, -1)} not found`,
                    },
                ],
                errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                404,
                errorId
            );

            return res.status(404).json(errorResponse);
        }

        logger.info(`Fetched ${resourceType.slice(0, -1)} with ID: ${id}`);

        return res.status(200).json({
            success: true,
            message: `${resourceType.slice(0, -1)} fetched successfully`,
            [resourceType.slice(0, -1)]: item,
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();

        logger.error(
            `Error fetching item. ID: ${req.params.productId || req.params.discountId}. Message: ${err.message}`,
            {
                errorId,
            }
        );

        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );

        return res.status(500).json(errorResponse);
    }
};

const UpdatePackagesAndDiscountById = async (req, res) => {
    let id, existingItem, updatedItem, errorField;

    try {
        const pathSegments = req.path.split('/').filter(Boolean);
        const resourceType = pathSegments[0]; // 'packages' or 'discounts'

        if (resourceType === 'packages') {
            id = req.params.productId;
            errorField = 'productId';
            existingItem = await PackageService.getPackageById(id);
        } else if (resourceType === 'discounts') {
            id = req.params.discountId;
            errorField = 'discountId';
            existingItem = await DiscountService.getDiscountById(id);
        } else {
            return res.status(400).json({
                success: false,
                message: 'Invalid resource type for update',
            });
        }

        if (!existingItem) {
            const errorId = errorUtil.generateErrorId();
            logger.warn(
                `${resourceType.slice(0, -1)} with ID ${id} not found for update`,
                {
                    errorId,
                }
            );

            const errorResponse = errorUtil.createErrorResponse(
                [
                    {
                        field: errorField,
                        message: `${resourceType.slice(0, -1)} not found for update`,
                    },
                ],
                errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                404,
                errorId
            );

            return res.status(404).json(errorResponse);
        }

        updatedItem =
            resourceType === 'packages'
                ? await PackageService.updatePackageById(id, req.body)
                : await DiscountService.updateDiscount(id, req.body);

        logger.info(
            `${resourceType.slice(0, -1)} with ID ${id} updated successfully`
        );

        return res.status(200).json({
            success: true,
            message: `${resourceType.slice(0, -1)} updated successfully`,
            [resourceType.slice(0, -1)]: updatedItem,
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();

        logger.error(
            `Error updating item with ID ${req.params.productId || req.params.discountId}. Message: ${err.message}`,
            { errorId }
        );

        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );

        return res.status(500).json(errorResponse);
    }
};

const deletePackagesAndDiscountById = async (req, res) => {
    try {
        const pathSegments = req.path.split('/').filter(Boolean);
        const resourceType = pathSegments[0].toLowerCase(); // 'packages' or 'discounts'
        const resourceName = resourceType.slice(0, -1); // 'package' or 'discount'

        let id, existingItem, errorField;

        if (resourceType === 'packages') {
            id = req.params.productId;
            existingItem = await PackageService.getPackageById(id);
            errorField = 'productId';
        } else if (resourceType === 'discounts') {
            id = req.params.discountId;
            existingItem = await DiscountService.getDiscountById(id);
            errorField = 'discountId';
        } else {
            return res.status(400).json({
                success: false,
                message: 'Invalid resource type for deletion',
            });
        }

        if (!existingItem) {
            const errorId = errorUtil.generateErrorId();
            logger.warn(
                `${resourceName} with ID ${id} not found for deletion`,
                { errorId }
            );

            const errorResponse = errorUtil.createErrorResponse(
                [
                    {
                        field: errorField,
                        message: `${resourceName} not found for deletion`,
                    },
                ],
                errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                404,
                errorId
            );
            return res.status(404).json(errorResponse);
        }

        if (resourceType === 'packages') {
            await PackageService.deletePackageById(id);
        } else {
            await DiscountService.deleteDiscount(id);
        }

        logger.info(`${resourceName} with ID ${id} deleted successfully`);
        return res.status(200).json({
            success: true,
            message: `${resourceName} deleted successfully`,
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error deleting item with ID ${req.params.productId || req.params.discountId}. Message: ${err.message}`,
            { errorId }
        );
        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

const checkPromoCodeExists = async (req, res) => {
    const { discountId, promoCode } = req.body;

    if (!discountId || !promoCode) {
        return res.status(400).json({
            success: false,
            message: 'Both discountId and promoCode are required',
        });
    }

    try {
        const result = await DiscountService.checkPromoCodeExists(
            discountId,
            promoCode
        );

        if (result.valid) {
            return res.status(200).json({
                success: true,
                message: 'Promo code is valid',
                code: result.code,
            });
        }

        return res.status(400).json({
            success: false,
            message: result.reason,
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();

        logger.error(
            `Error validating promo code for discountId ${discountId}. Message: ${err.message}`,
            { errorId }
        );

        const errorResponse = errorUtil.createErrorResponse(
            [{ field: 'promoCode', message: err.message }],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );

        return res.status(500).json(errorResponse);
    }
};

module.exports = {
    createPackagesAndDiscount,
    getPackagesAndDiscount,
    getPackagesAndDiscountById,
    UpdatePackagesAndDiscountById,
    deletePackagesAndDiscountById,
    checkPromoCodeExists,
    getPackagesAndDiscounts,
};
