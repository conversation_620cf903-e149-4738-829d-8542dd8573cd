/* eslint-disable no-unused-vars */
/* eslint-disable id-length */
const fs = require('fs');
const path = require('path');
const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3');
const mime = require('mime-types');
require('dotenv').config();

// === S3 Configuration ===
const S3_BUCKET = process.env.VITE_S3_BUCKET_NAME;
const REGION = process.env.REGION;
const ACCESS_KEY = process.env.ACCESSKEY_ID;
const SECRET_KEY = process.env.SECRETACCESSKEY;
const CDN_URL = process.env.VITE_S3_CDN_URL;

const s3 = new S3Client({
    region: REGION,
    credentials: {
        accessKeyId: ACCESS_KEY,
        secretAccessKey: SECRET_KEY,
    },
});

// === S3 Key Generator ===
function generateS3Key({
    baseFolder,
    mainFolder,
    subFolder,
    nestedPath,
    fileName,
}) {
    let key = `${baseFolder}/`;
    if (subFolder) key += `${subFolder}/`;
    key += `${mainFolder}/`;
    if (nestedPath) key += `${nestedPath}/`;
    key += `${Date.now()}-${fileName}`;
    return key;
}

// === Upload Function ===
async function uploadFileToS3FromDisk(localFilePath, options) {
    if (!fs.existsSync(localFilePath)) {
        throw new Error(`File not found: ${localFilePath}`);
    }

    const fileBuffer = fs.readFileSync(localFilePath);
    const contentType =
        mime.lookup(localFilePath) || 'application/octet-stream';
    const key = generateS3Key(options);

    const params = {
        Bucket: S3_BUCKET,
        Key: key,
        Body: fileBuffer,
        ContentType: contentType,
    };

    const command = new PutObjectCommand(params);
    await s3.send(command);

    const s3Url = `https://${S3_BUCKET}.s3.${REGION}.amazonaws.com/${key}`;
    const cdnUrl = s3Url.replace(
        `https://${S3_BUCKET}.s3.${REGION}.amazonaws.com/`,
        CDN_URL
    );

    return {
        key,
        url: cdnUrl,
    };
}

module.exports = {
    uploadFileToS3FromDisk,
    generateS3Key,
};
