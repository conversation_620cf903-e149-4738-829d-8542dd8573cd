const dotenv = require('dotenv');

dotenv.config();

// const COMPANY_EMAIL = process.env.COMPANY_EMAIL;

const LOGO = 'https://cdn.staging.gigmosaic.ca/common/1.png';

const CreateBookingEmailTemplate = (
    referenceCode,
    bookingData,
    providerData,
    serviceData
) => {
    const {
        appointmentTimeFrom,
        appointmentTimeTo,
        appointmentDate,
        personalInfo,
        subtotal,
        tax,
        discount,
        total,
    } = bookingData;

    const { firstName, lastName } = personalInfo;
    const { name } = providerData.user;

    const { serviceTitle } = serviceData;

    const newHtmlbody = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Opinion Status Update</title>
    <!-- Google Font -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family:  'Open Sans', sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 40px auto;
            background-color: #ffffff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        .logo {
            text-align: center;
            margin-bottom: 20px;
        }
        .logo img {
            max-width: 180px;
        }
        .content {
            text-align: center;
            color: #333;
        }
        h2 {
            color: #2167B5;
            margin-bottom: 20px;
        }
        .status-label {
            display: inline-block;
            margin: 10px 0 20px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
            background-color: #2167B5;
            border-radius: 5px;
        }
        .cta-button {
            display: inline-block;
            margin-top: 20px;
            padding: 12px 24px;
            background-color: #2167B5;
            color: #ffffff;
            text-decoration: none;
            font-weight: 500;
            border-radius: 4px;
            transition: background-color 0.3s ease;
        }
        .cta-button:hover {
            background-color: #184e8c;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            font-size: 13px;
            color: #666666;
            line-height: 1.6;
        }
        .footer a {
            color: #2167B5;
            text-decoration: none;
        }
        .details {
      margin-top: 20px;
      font-size: 14px;
      color: #555;
      text-align: left;
    }
    .details p {
      margin: 8px 0;
    }
    p {
            font-size: 14px;
        }
 .booking-summary {
  margin-top: 30px;
  background-color: #f9f9f9;
  border-radius: 6px;
  padding: 20px;
  border: 1px solid #e0e0e0;
}

.summary-heading {
  font-size: 16px;
  font-weight: 600;
  color: #2167B5;
  margin-bottom: 20px;
  border-bottom: 1px solid #ddd;
  padding-bottom: 10px;
}

.summary-item {
  margin-bottom: 12px;
}

.label {
  font-weight: 600;
  font-size: 14px;
  margin: 0;
  color: #333;
}

.label .value {
  font-weight: 400;
  color: #555;
  margin-left: 5px;
}

.label.total {
  font-size: 15px;
  color: #000;
}

.label.total .value {
  font-weight: 600;
  color: #2167B5;
}


    </style>
</head>
<body>

 <div class="container">
    <div class="logo">
      <img src="https://cdn.staging.gigmosaic.ca/common/1.png" alt="Gigmosaic Logo" />
    </div>

    <h2>Booking Successfully created – ${referenceCode}</h2>

    <p>Dear User,</p>

    <p>We are pleased to confirm your booking for <strong>${serviceTitle}</strong> scheduled on <strong>${appointmentDate}</strong> at <strong>${appointmentTimeFrom}-${appointmentTimeTo}</strong>.</p>

    <p>Please find the details of your booking below:</p>

<div class="booking-summary">
  <p class="summary-heading">📄 Booking Summary</p>

  <div class="summary-item">
    <p class="label">Booking Reference: <span class="value">${referenceCode}</span></p>
  </div>

  <div class="summary-item">
    <p class="label">Service: <span class="value">${serviceTitle}</span></p>
  </div>

  <div class="summary-item">
    <p class="label">Date: <span class="value">${appointmentDate}</span></p>
  </div>

  <div class="summary-item">
    <p class="label">Time: <span class="value">${appointmentTimeFrom} – ${appointmentTimeTo}</span></p>
  </div>

  <div class="summary-item">
    <p class="label">Customer Name: <span class="value">${firstName} ${lastName}</span></p>
  </div>

  <div class="summary-item">
    <p class="label">Provider Name: <span class="value">${name}</span></p>
  </div>

  <div class="summary-item">
    <p class="label">Subtotal: <span class="value">$${subtotal}</span></p>
  </div>

  <div class="summary-item">
    <p class="label">Tax: <span class="value">$${tax}</span></p>
  </div>

  <div class="summary-item">
    <p class="label">Discount: <span class="value">$${discount}</span></p>
  </div>

  <div class="summary-item">
    <p class="label total">Total: <span class="value">$${total}</span></p>
  </div>
</div>





<div  style="margin-top: 30px;">
      <p>If you have any questions or need assistance, feel free to contact our support team at 
        <a href="mailto:<EMAIL>" style="color: #2167B5;"><EMAIL></a>.
      </p>
      <p>Thank you for using Gig Mosaic!</p>
    </div>
    <div class="footer">
        <div><strong>Company:</strong> Gig Mosaic</div>
        <div><strong>Email:</strong> <EMAIL></div>
        <div><strong>Website:</strong> <a href="https://www.gigmosaic.com" target="_blank">www.gigmosaic.com</a></div>
       <div>&copy; 2025 Gig Mosaic. All rights reserved.</div>
 </div>
</div>

</body>
</html>

 `;

    const newSubject = `📅 Your Appointment Has Been created Successfully ✅`;

    return { newSubject, newHtmlbody };
};

module.exports = CreateBookingEmailTemplate;
