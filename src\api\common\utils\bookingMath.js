const calculateAdditionalServicesTotal = (
    service,
    additionalServiceIds = []
) => {
    if (!service?.additionalServices) return 0;

    return service.additionalServices
        .filter((svc) => additionalServiceIds.includes(svc.id))
        .reduce((sum, svc) => sum + (svc.price || 0), 0);
};

const getServicePrice = (service) => {
    return (
        service.priceAfterDiscount ?? service.offerPrice ?? service.price ?? 0
    );
};

const calculateTax = (subtotal, taxRate = 0) => {
    return subtotal * taxRate;
};

const calculateDiscount = async (
    discountId,
    subtotal,
    DiscountService,
    logger,
    customerId,
    PackageService,
    updateDatabase = true
) => {
    if (!discountId) return 0;

    const discountDoc = await DiscountService.getDiscountById(discountId);

    // ✅ Must be either a discount or a package discount
    if (!discountDoc?.isDiscount && !discountDoc?.isPackageDiscount) {
        logger?.info?.('Invalid discount type');
        return 0;
    }

    if (discountDoc.maxCount <= 0) {
        logger?.info?.('Discount maxCount reached, discount not applied');
        return 0;
    }

    const now = new Date();

    if (discountDoc.durationType === 'time-base') {
        const startDate = new Date(discountDoc.duration.start);
        const endDate = new Date(discountDoc.duration.end);
        endDate.setHours(23, 59, 59, 999); // inclusive

        if (now < startDate || now > endDate) {
            logger?.info?.('Time-based discount expired or not started yet');
            return 0;
        }
    }

    let discountValue = 0;

    if (discountDoc.valueType === 'percentage') {
        discountValue = (subtotal * discountDoc.amount) / 100;
    } else if (discountDoc.valueType === 'amount') {
        discountValue = discountDoc.amount;
    }

    discountValue = Math.min(discountValue, subtotal);

    if (discountValue <= 0) return 0;

    if (updateDatabase) {
        const updatedMaxCount = discountDoc.maxCount - 1;

        const updatePayload = {
            maxCount: updatedMaxCount,
            soldOut: updatedMaxCount === 0,
            acceptAllUserIds: [
                ...(discountDoc.acceptAllUserIds || []),
                customerId,
            ],
        };

        await DiscountService.updateDiscount(discountId, updatePayload);

        if (discountDoc.isPackageDiscount && updatedMaxCount === 0) {
            // await PackageService.updatePackageById(discountDoc.packageId, {
            //     isSoldOut: true,
            // });
        }

        logger?.info?.(
            `Discount maxCount decremented to ${updatedMaxCount}` +
                (updatedMaxCount === 0 ? ' and marked as sold out' : '')
        );
    } else {
        logger?.info?.('Discount calculated but not applied (preview only)');
    }

    return discountValue;
};

module.exports = {
    calculateAdditionalServicesTotal,
    getServicePrice,
    calculateTax,
    calculateDiscount,
};
