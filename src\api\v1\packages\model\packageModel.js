const mongoose = require('mongoose');

const { Schema } = mongoose;

const packageSchema = new Schema(
    {
        packageId: { type: String, required: true, unique: true },

        serviceId: {
            type: String,
        },

        providerId: { type: String },

        isDiscount: { type: Boolean, default: false },

        packageName: { type: String, required: true },

        price: { type: Number, required: true },

        includes: {
            type: [String],
            default: [],
        },

        discountId: { type: String },

        isSoldOut: { type: Boolean, default: false },
    },
    { timestamps: true }
);

module.exports = mongoose.model('Package', packageSchema);

// const Discount = require('./models/discountModel');
// const Package = require('./models/packageModel');

// // Create discount
// await Discount.create({
//     discountId: 'discount_0001',
//     isDiscount: true,
//     discountType: 'promo-code',
//     valueType: 'percentage',
//     durationType: 'time-base',
//     amount: 20,
//     duration: { start: '2025-08-01', end: '2025-09-01' },
//     promoCode: 'SUMMER25',
//     createdBy: 'admin123',
// });

// // Create package
// await Package.create({
//     packageId: 'package_0001',
//     isDiscount: false,
//     packageName: 'Premium Plan',
//     price: 99,
//     includes: ['feature1', 'feature2'],
//     discount: {
//         discountId: 'discount_0001',
//         discountType: 'promo-code',
//         valueType: 'percentage',
//         durationType: 'time-base',
//         amount: 20,
//         duration: { start: '2025-08-01', end: '2025-09-01' },
//         promoCode: 'SUMMER25',
//     },
//     createdBy: 'admin123',
// });
