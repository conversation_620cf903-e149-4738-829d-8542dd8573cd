/* eslint-disable consistent-return */
// validators.js
const { body, validationResult } = require('express-validator');

/**
 * Base discount fields, paramPrefix allows reuse for nested structures.
 * @param {string} [paramPrefix] - Prefix for nested keys like 'discount.'
 */
const buildDiscountValidator = (paramPrefix = '') => [
    body(`${paramPrefix}isDiscount`)
        .isBoolean()
        .withMessage(`${paramPrefix}isDiscount must be a boolean`),

    body(`${paramPrefix}discountType`)
        .isIn(['promo-code', 'general-discount'])
        .withMessage(
            `${paramPrefix}discountType must be either 'promo-code' or 'general-discount'`
        ),

    body(`${paramPrefix}valueType`)
        .isIn(['amount', 'percentage'])
        .withMessage(
            `${paramPrefix}valueType must be 'amount' or 'percentage'`
        ),

    body(`${paramPrefix}durationType`)
        .isIn(['life-time', 'time-base'])
        .withMessage(
            `${paramPrefix}durationType must be 'life-time' or 'time-base'`
        ),

    body(`${paramPrefix}amount`)
        .isNumeric()
        .withMessage(`${paramPrefix}amount must be a number`),

    body(`${paramPrefix}duration.start`)
        .notEmpty()
        .withMessage(`${paramPrefix}duration.start is required`),

    body(`${paramPrefix}duration.end`)
        .optional()
        .isString()
        .withMessage(`${paramPrefix}duration.end must be a string`),

    body(`${paramPrefix}promoCode`)
        .optional()
        .isString()
        .withMessage(`${paramPrefix}promoCode must be a string`),
];

// Discount validator for discount object at root
const discountValidator = buildDiscountValidator();

// Package validator (validates package + nested discount)
const packageValidator = [
    body('packageName').notEmpty().withMessage('packageName is required'),
    body('price').isNumeric().withMessage('price must be a number'),
    body('includes')
        .optional()
        .isArray()
        .withMessage('includes must be an array of strings'),

    // nested discount validator with prefix 'discount.'
    ...buildDiscountValidator('discount.'),
];

module.exports = {
    buildDiscountValidator,
    discountValidator,
    packageValidator,
};

/* *
 * Conditionally runs discount or package validators depending on isDiscount flag.
 */
const conditionalValidator = async (req, res, next) => {
    const isDiscountRoot = req.body.isDiscount === true;
    const isDiscountNested = req.body.discount?.isDiscount === true;

    if (isDiscountRoot) {
        // Validate discount fields only at root level
        await Promise.all(
            discountValidator.map((validator) => validator.run(req))
        );
    } else if (isDiscountNested) {
        // Validate only nested discount object (inside package)
        const nestedDiscountValidator = buildDiscountValidator('discount.');
        await Promise.all(
            nestedDiscountValidator.map((validator) => validator.run(req))
        );
    } else {
        // Validate package (including nested discount validation)
        await Promise.all(
            packageValidator.map((validator) => validator.run(req))
        );
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }

    next();
};

function routeIdentifier(type) {
    return function (req, res, next) {
        req.routeType = type;
        next();
    };
}

module.exports = { conditionalValidator, routeIdentifier };
