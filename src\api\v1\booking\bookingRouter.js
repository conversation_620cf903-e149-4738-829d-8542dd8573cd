const express = require('express');

const router = express.Router();

const BookingController = require('./bookingController');

const { validateBooking } = require('./bookingMiddleware');

const fetchAuth = require('../../common/utils/communicator');

router.post(
    '/',
    validateBooking,
    fetchAuth.fetchAuthAllDataMiddleware,
    BookingController.createBooking
);

router.post(
    '/calculateTotals',
    fetchAuth.fetchAuthAllDataMiddleware,
    BookingController.calculateBookingTotals
);

router.post(
    '/checkAvailability',
    fetchAuth.fetchAuthAllDataMiddleware,
    BookingController.checkAvailabilityByService
);

router.post(
    '/checkAvailableStaff',
    fetchAuth.fetchAuthAllDataMiddleware,
    BookingController.checkAvailableStaff
);

router.post(
    '/checkAvailabilityDateRange',
    fetchAuth.fetchAuthAllDataMiddleware,
    BookingController.checkAvailabilityDateRange
);
router.post(
    '/checkAvailableStaffForTimeSlot',
    fetchAuth.fetchAuthAllDataMiddleware,
    BookingController.checkAvailableStaffForTimeSlot
);

router.get(
    '/',
    fetchAuth.fetchAuthAllDataMiddleware,
    BookingController.getBookings
);

router.get('/getConfirmedBookings', BookingController.getConfirmedBookings);

router.get(
    '/:bookingId',
    fetchAuth.fetchAuthAllDataMiddleware,
    BookingController.getBookingById
);

router.get(
    '/referenceCode/:referenceCode',
    fetchAuth.fetchAuthAllDataMiddleware,
    BookingController.getBookingByReferenceCode
);

router.put(
    '/:bookingId',
    validateBooking,
    fetchAuth.fetchAuthAllDataMiddleware,
    BookingController.updateBooking
);

router.put(
    '/:bookingId/reschedule',
    fetchAuth.fetchAuthAllDataMiddleware,
    BookingController.rescheduleBooking
);

router.put(
    '/:bookingId/status',
    fetchAuth.fetchAuthAllDataMiddleware,
    BookingController.updateBookingStatus
);

router.delete(
    '/:bookingId',
    fetchAuth.fetchAuthAllDataMiddleware,
    BookingController.deleteBooking
);

module.exports = router;
