const request = require('supertest');
const app = require('../../../src/app');
const Review = require('../../../src/api/v1/Reviews/model/reviewModel');
const { ReviewCounter } = require('../../../src/api/v1/Reviews/counter/reviewCounter');

// Mock authentication middleware
jest.mock('../../../src/api/common/utils/communicator', () => ({
    fetchAuthAllDataMiddleware: (req, res, next) => {
        req.userData = {
            user: {
                userId: 'CUST_001',
                userType: 'customer'
            }
        };
        next();
    },
    fetchAuthProviderDataMiddleware: (req, res, next) => {
        req.userData = {
            user: {
                userId: 'PRV_001',
                userType: 'provider'
            }
        };
        next();
    },
    fetchAuthAdminDataMiddleware: (req, res, next) => {
        req.userData = {
            user: {
                userId: 'ADM_001',
                userType: 'admin'
            }
        };
        next();
    }
}));

describe('Review Routes Integration Tests', () => {
    beforeEach(async () => {
        // Clear database before each test
        await Review.deleteMany({});
        await ReviewCounter.deleteMany({});
    });

    describe('POST /api/v1/reviews', () => {
        it('should create a new review successfully', async () => {
            const reviewData = {
                serviceId: 'SRV_001',
                bookingId: 'BKG_001',
                providerId: 'PRV_001',
                rating: 5,
                title: 'Excellent service',
                comment: 'The service was outstanding and exceeded my expectations. Highly recommended!',
                images: [
                    {
                        imageUrl: 'https://example.com/image1.jpg',
                        imageCaption: 'Before photo'
                    }
                ]
            };

            const response = await request(app)
                .post('/api/v1/reviews')
                .send(reviewData)
                .expect(201);

            expect(response.body.success).toBe(true);
            expect(response.body.message).toBe('Review created successfully');
            expect(response.body.review).toHaveProperty('reviewId');
            expect(response.body.review.rating).toBe(5);
            expect(response.body.review.comment).toBe(reviewData.comment);
        });

        it('should return validation error for invalid rating', async () => {
            const reviewData = {
                serviceId: 'SRV_001',
                bookingId: 'BKG_001',
                providerId: 'PRV_001',
                rating: 6, // Invalid rating
                comment: 'Good service'
            };

            const response = await request(app)
                .post('/api/v1/reviews')
                .send(reviewData)
                .expect(422);

            expect(response.body.success).toBe(false);
            expect(response.body.errors).toEqual(
                expect.arrayContaining([
                    expect.objectContaining({
                        field: 'rating',
                        message: 'rating must be an integer between 1 and 5.'
                    })
                ])
            );
        });

        it('should return validation error for missing required fields', async () => {
            const reviewData = {
                serviceId: 'SRV_001',
                // Missing bookingId, rating, comment
            };

            const response = await request(app)
                .post('/api/v1/reviews')
                .send(reviewData)
                .expect(422);

            expect(response.body.success).toBe(false);
            expect(response.body.errors.length).toBeGreaterThan(0);
        });
    });

    describe('GET /api/v1/reviews', () => {
        beforeEach(async () => {
            // Create test reviews
            const testReviews = [
                {
                    reviewId: 'REV_000001',
                    customerId: 'CUST_001',
                    providerId: 'PRV_001',
                    serviceId: 'SRV_001',
                    bookingId: 'BKG_001',
                    rating: 5,
                    title: 'Excellent',
                    comment: 'Great service',
                    status: 'approved',
                    isDeleted: false
                },
                {
                    reviewId: 'REV_000002',
                    customerId: 'CUST_002',
                    providerId: 'PRV_001',
                    serviceId: 'SRV_001',
                    bookingId: 'BKG_002',
                    rating: 4,
                    title: 'Good',
                    comment: 'Good service',
                    status: 'approved',
                    isDeleted: false
                }
            ];

            await Review.insertMany(testReviews);
        });

        it('should fetch reviews successfully', async () => {
            const response = await request(app)
                .get('/api/v1/reviews')
                .expect(200);

            expect(response.body.success).toBe(true);
            expect(response.body.message).toBe('Reviews fetched successfully');
            expect(response.body.reviews).toHaveLength(2);
            expect(response.body.total).toBe(2);
            expect(response.body.page).toBe(1);
        });

        it('should filter reviews by service ID', async () => {
            const response = await request(app)
                .get('/api/v1/reviews?serviceId=SRV_001')
                .expect(200);

            expect(response.body.success).toBe(true);
            expect(response.body.reviews).toHaveLength(2);
            expect(response.body.reviews.every(review => review.serviceId === 'SRV_001')).toBe(true);
        });

        it('should filter reviews by rating', async () => {
            const response = await request(app)
                .get('/api/v1/reviews?rating=5')
                .expect(200);

            expect(response.body.success).toBe(true);
            expect(response.body.reviews).toHaveLength(1);
            expect(response.body.reviews[0].rating).toBe(5);
        });

        it('should paginate reviews correctly', async () => {
            const response = await request(app)
                .get('/api/v1/reviews?page=1&limit=1')
                .expect(200);

            expect(response.body.success).toBe(true);
            expect(response.body.reviews).toHaveLength(1);
            expect(response.body.page).toBe(1);
            expect(response.body.pages).toBe(2);
            expect(response.body.hasNext).toBe(true);
            expect(response.body.hasPrev).toBe(false);
        });
    });

    describe('GET /api/v1/reviews/:reviewId', () => {
        let testReview;

        beforeEach(async () => {
            testReview = await Review.create({
                reviewId: 'REV_000001',
                customerId: 'CUST_001',
                providerId: 'PRV_001',
                serviceId: 'SRV_001',
                bookingId: 'BKG_001',
                rating: 5,
                title: 'Excellent',
                comment: 'Great service',
                status: 'approved',
                isDeleted: false
            });
        });

        it('should fetch review by ID successfully', async () => {
            const response = await request(app)
                .get(`/api/v1/reviews/${testReview.reviewId}`)
                .expect(200);

            expect(response.body.success).toBe(true);
            expect(response.body.message).toBe('Review fetched successfully');
            expect(response.body.review.reviewId).toBe(testReview.reviewId);
            expect(response.body.review.rating).toBe(5);
        });

        it('should return 404 for non-existent review', async () => {
            const response = await request(app)
                .get('/api/v1/reviews/REV_999999')
                .expect(404);

            expect(response.body.success).toBe(false);
            expect(response.body.errors[0].message).toBe('Review not found.');
        });
    });

    describe('GET /api/v1/reviews/analytics', () => {
        beforeEach(async () => {
            // Create test reviews for analytics
            const testReviews = [
                {
                    reviewId: 'REV_000001',
                    customerId: 'CUST_001',
                    providerId: 'PRV_001',
                    serviceId: 'SRV_001',
                    bookingId: 'BKG_001',
                    rating: 5,
                    comment: 'Excellent',
                    status: 'approved',
                    isDeleted: false,
                    helpfulCount: 3
                },
                {
                    reviewId: 'REV_000002',
                    customerId: 'CUST_002',
                    providerId: 'PRV_001',
                    serviceId: 'SRV_001',
                    bookingId: 'BKG_002',
                    rating: 4,
                    comment: 'Good',
                    status: 'approved',
                    isDeleted: false,
                    helpfulCount: 2
                },
                {
                    reviewId: 'REV_000003',
                    customerId: 'CUST_003',
                    providerId: 'PRV_001',
                    serviceId: 'SRV_001',
                    bookingId: 'BKG_003',
                    rating: 3,
                    comment: 'Average',
                    status: 'approved',
                    isDeleted: false,
                    helpfulCount: 1
                }
            ];

            await Review.insertMany(testReviews);
        });

        it('should fetch analytics for service successfully', async () => {
            const response = await request(app)
                .get('/api/v1/reviews/analytics?serviceId=SRV_001')
                .expect(200);

            expect(response.body.success).toBe(true);
            expect(response.body.message).toBe('Review analytics fetched successfully');
            expect(response.body.analytics).toHaveProperty('totalReviews');
            expect(response.body.analytics).toHaveProperty('averageRating');
            expect(response.body.analytics).toHaveProperty('ratingBreakdown');
            expect(response.body.analytics.totalReviews).toBe(3);
            expect(response.body.analytics.averageRating).toBe(4);
        });

        it('should return error when no filters provided', async () => {
            const response = await request(app)
                .get('/api/v1/reviews/analytics')
                .expect(400);

            expect(response.body.success).toBe(false);
            expect(response.body.errors[0].message).toBe('Either serviceId or providerId is required');
        });
    });
});
