const dotenv = require('dotenv');

dotenv.config();

const DeleteBookingEmailTemplate = (referenceCode) => {
    const subject = `Booking Deleted for ${referenceCode}`;

    const htmlbody = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Booking Cancellation Notice</title>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans:wght@400;700&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: 'Open Sans', sans-serif;
      background-color: #f4f4f4;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 40px auto;
      background-color: #ffffff;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }
    .logo {
      text-align: center;
      margin-bottom: 20px;
    }
    .logo img {
      max-width: 180px;
    }
    .content {
      text-align: center;
      color: #333;
    }
    h2 {
      color: #D9534F;
      margin-bottom: 20px;
    }
    .status-label {
      display: inline-block;
      margin: 10px 0 20px;
      padding: 12px 24px;
      font-size: 16px;
      font-weight: 600;
      color: #ffffff;
      background-color: #D9534F;
      border-radius: 5px;
    }
    .footer {
      text-align: center;
      margin-top: 40px;
      font-size: 13px;
      color: #666666;
      line-height: 1.6;
    }
    .footer a {
      color: #2167B5;
      text-decoration: none;
    }
    .details {
      margin-top: 20px;
      font-size: 14px;
      color: #555;
      text-align: left;
    }
    .details p {
      margin: 8px 0;
    }
    p {
      font-size: 14px;
    }
  </style>
</head>
<body>

<div class="container">
  <div class="logo">
      <img src="https://cdn.staging.gigmosaic.ca/common/1.png" alt="Gigmosaic Logo">  
  </div>

  <div class="content">
    <h2>Booking Cancelled - ${referenceCode}</h2>
  </div>

  <p>Dear User,</p>
  <p>We’d like to inform you that your booking has been <strong>successfully cancelled</strong> and is no longer active on our platform.</p>
  <p>If this was unintentional or if you have questions, please reach out to our support team for assistance.</p>

  <div class="content">
        <p><strong>Booking ID:</strong> <span style="color: #D9534F;">${referenceCode}</span></p> <!-- Replace dynamically -->

    <p><strong>Status:</strong></p>
    <div class="status-label">Cancelled</div>
  </div>

  <div class="details">
    <p>Need help or want to rebook?</p>
    <p>Contact us at <a href="mailto:<EMAIL>" style="color: #2167B5;"><EMAIL></a> and we’ll be happy to assist you.</p>
    <p>Thank you for using Gig Mosaic.</p>
  </div>

  <div class="footer">
    <div><strong>Company:</strong> Gig Mosaic</div>
    <div><strong>Email:</strong> <EMAIL></div>
    <div><strong>Website:</strong> <a href="https://www.gigmosaic.com" target="_blank">www.gigmosaic.com</a></div>
    <div>&copy; 2025 Gig Mosaic. All rights reserved.</div>
  </div>
</div>

</body>
</html>
 `;

    return { subject, htmlbody };
};

module.exports = DeleteBookingEmailTemplate;
