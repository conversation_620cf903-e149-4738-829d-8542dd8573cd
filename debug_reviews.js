const mongoose = require('mongoose');
require('dotenv').config();

// Import the Review model
const Review = require('./src/api/v1/Reviews/model/reviewModel');
const ReviewService = require('./src/api/v1/Reviews/service/reviewService');

async function debugReviews() {
    try {
        console.log('Connecting to MongoDB...');
        await mongoose.connect(process.env.MONGODB_URL);
        console.log('Connected to MongoDB successfully');
        
        // Get all reviews to see their structure
        console.log('\n=== Fetching all reviews ===');
        const reviews = await Review.find({}).limit(5);
        console.log(`Found ${reviews.length} reviews in database`);
        
        reviews.forEach((review, index) => {
            console.log(`\nReview ${index + 1}:`);
            console.log('  MongoDB _id:', review._id.toString());
            console.log('  Custom reviewId:', review.reviewId);
            console.log('  isDeleted:', review.isDeleted);
            console.log('  status:', review.status);
            console.log('  customerId:', review.customerId);
            console.log('  providerId:', review.providerId);
            console.log('  title:', review.title);
        });
        
        // Test the getReviewById service method with different ID formats
        if (reviews.length > 0) {
            const testReview = reviews[0];
            
            console.log('\n=== Testing getReviewById service method ===');
            
            // Test with custom reviewId
            console.log(`\nTesting with custom reviewId: ${testReview.reviewId}`);
            try {
                const result1 = await ReviewService.getReviewById(testReview.reviewId);
                console.log('✅ Success with custom reviewId');
                console.log('  Returned reviewId:', result1.reviewId);
            } catch (error) {
                console.log('❌ Failed with custom reviewId:', error.message);
            }
            
            // Test with MongoDB _id
            console.log(`\nTesting with MongoDB _id: ${testReview._id.toString()}`);
            try {
                const result2 = await ReviewService.getReviewById(testReview._id.toString());
                console.log('✅ Success with MongoDB _id');
                console.log('  Returned reviewId:', result2.reviewId);
            } catch (error) {
                console.log('❌ Failed with MongoDB _id:', error.message);
            }
        }
        
        // Test the getReviewsForFrontend method to see what IDs it returns
        console.log('\n=== Testing getReviewsForFrontend method ===');
        try {
            const frontendResult = await ReviewService.getReviewsForFrontend({ limit: 3 });
            console.log(`✅ getReviewsForFrontend returned ${frontendResult.reviews.length} reviews`);
            
            frontendResult.reviews.forEach((review, index) => {
                console.log(`\nFrontend Review ${index + 1}:`);
                console.log('  reviewId:', review.reviewId);
                console.log('  _id:', review._id ? review._id.toString() : 'Not included');
                console.log('  title:', review.title);
            });
        } catch (error) {
            console.log('❌ getReviewsForFrontend failed:', error.message);
        }
        
        await mongoose.disconnect();
        console.log('\n=== Debug completed ===');
        
    } catch (error) {
        console.error('Error:', error.message);
        console.error('Stack:', error.stack);
    }
}

debugReviews();
