/* eslint-disable consistent-return */
const mongoose = require('mongoose');

const onlineMeetingSchema = new mongoose.Schema({
    platform: {
        type: String,
        enum: ['Zoom', 'GoogleMeet'],
        required: true,
    },
    invitationLink: {
        type: String,
        trim: true,
        validate: {
            validator: function (value) {
                return value ? /^https?:\/\/.*/.test(value) : true;
            },
            message: 'Invalid meeting invitation link.',
        },
    },
    allowMeeting: {
        type: Boolean,
        default: false,
    },
});
const serviceInformationSchema = new mongoose.Schema(
    {
        serviceId: {
            type: String,
            required: true,
        },

        providerId: {
            type: String,
            required: true,
            default: 'AID_1',
        },

        serviceTitle: {
            type: String,
            required: true,
        },
        slug: {
            type: String,
            required: true,
        },
        categoryId: {
            type: String,
            required: true,
        },
        subCategoryId: {
            type: String,
            required: true,
        },
        price: {
            type: Number,
            default: 0,
            min: 0,
        },
        isOffers: {
            type: Boolean,
            required: true,
            default: false,
        },

        offerPrice: {
            type: Number,
            default: 0,
            min: 0,
        },
        priceAfterDiscount: {
            type: Number,
            default: 0,
            min: 0,
        },

        duration: {
            type: String,
        },

        onlineMeetings: [onlineMeetingSchema],

        staff: {
            type: [String],
        },

        includes: {
            type: [String],
        },

        serviceOverview: {
            type: String,
            required: true,
        },

        isActive: {
            type: Boolean,
            required: true,
            default: true,
        },

        isAdditional: {
            type: Boolean,
            required: true,
            default: false,
        },

        additionalServicesId: {
            type: [String],
            default: [],
            required: true,
        },
        serviceAvailableId: {
            type: [String],
            default: [],
            required: true,
        },
        locationId: {
            type: [String],
            default: [],
            required: true,
        },

        galleryId: {
            type: [String],
            default: [],
            required: true,
        },
        faqId: {
            type: [String],
            default: [],
            required: true,
        },

        seoId: {
            type: [String],
            default: [],
            required: true,
        },

        packageId: {
            type: [String],
            default: [],
            required: true,
        },

        discountId: {
            type: String,
        },

        isDiscount: {
            type: Boolean,
            required: true,
            default: false,
        },

        isPackage: {
            type: Boolean,
            required: true,
            default: false,
        },
    },
    {
        timestamps: true,
    }
);

serviceInformationSchema.pre('save', function (next) {
    if (!this.isOffers) {
        this.offerPrice = 0;
    }

    if (this.isOffers && this.offerPrice <= 0) {
        return next(
            new Error('offerPrice must be greater than 0 if isOffers is true.')
        );
    }

    next();
});
const ServiceInformation = mongoose.model(
    'ServiceInformation',
    serviceInformationSchema
);

module.exports = ServiceInformation;
