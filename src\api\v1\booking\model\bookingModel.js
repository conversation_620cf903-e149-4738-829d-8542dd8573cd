const mongoose = require('mongoose');

// const cartItemSchema = new mongoose.Schema(
//     {
//         serviceId: {
//             type: String,
//         },
//         name: { type: String },
//         duration: { type: String },
//         price: { type: Number },
//     },
//     { _id: false }
// );

const auditLogSchema = new mongoose.Schema(
    {
        action: { type: String },
        performedBy: { type: String },
        timestamp: { type: Date, default: Date.now },
    },
    { _id: false }
);

const personalInfoSchema = new mongoose.Schema(
    {
        firstName: { type: String },
        lastName: { type: String },
        email: { type: String },
        phone: { type: String },
        address: {
            street: { type: String },
            city: { type: String },
            state: { type: String },
            postalCode: { type: String },
        },
        bookingNotes: { type: String },
    },
    { _id: false }
);

// Main Booking Schema
const bookingSchema = new mongoose.Schema(
    {
        bookingId: { type: String },

        referenceCode: { type: String },

        customerId: { type: String },

        providerId: { type: String },

        serviceId: {
            type: String,
        },

        // Location
        locationId: { type: String },

        // Staff
        staffId: { type: String },

        // Additional Services
        additionalServiceIds: [{ type: String }],

        // Appointment

        timeSlotId: {
            type: String,
        },
        appointmentDate: { type: Date },

        appointmentTimeFrom: { type: String },

        appointmentTimeTo: { type: String },

        // Customer Info
        personalInfo: personalInfoSchema,

        // Payment
        paymentMethod: {
            type: String,
            enum: [
                'Wallet',
                'Cash on Delivery',
                'Credit/Debit Card',
                'Stripe',
                'PayPal',
                'Razorpay',
                'PaySolution',
                'Square',
            ],
        },

        isPaid: { type: Boolean, default: false },

        // Booking Info

        bookingStatus: {
            type: String,
            enum: [
                'Pending',
                'Confirmed',
                'Cancelled',
                'Completed',
                'Incomplete',
                'Inprogress',
            ],
            default: 'Incomplete',
        },

        note: { type: String },

        packageId: { type: String },

        discountId: { type: String },

        // Pricing
        subtotal: { type: Number },

        tax: { type: Number, default: 0 },

        discount: { type: Number, default: 0 },

        total: { type: Number },

        // Timeline
        bookingDate: { type: Date, default: Date.now },

        providerAcceptanceDate: { type: Date },

        completionDate: { type: Date },

        // Audit Logs
        auditLogs: { type: [auditLogSchema], default: [] },
    },
    { timestamps: true }
);

const Booking = mongoose.model('Booking', bookingSchema);

module.exports = Booking;
