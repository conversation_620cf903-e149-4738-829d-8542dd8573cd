// /* eslint-disable camelcase */
// /* eslint-disable id-length */
// // emailService.js
// const { SendMailClient } = require('zeptomail');

// const dotenv = require('dotenv');

// dotenv.config();

// const SOURCE = process.env.SOURCE;

// const url = process.env.EMAIL_URL;

// const token = process.env.EMAIL_TOKEN;

// const client = new SendMailClient({ url, token });

// /**
//  * Generic function to send an email.
//  * @param {string} toEmail - The recipient's email address.
//  * @param {string} subject - The subject of the email.
//  * @param {string} htmlbody - The HTML content of the email.
//  */
// const sendEmail = async (toEmail, subject, htmlbody) => {
//     await client.sendMail({
//         from: {
//             address: SOURCE,
//             name: 'noreply',
//         },
//         to: [
//             {
//                 email_address: {
//                     address: toEmail,
//                 },
//             },
//         ],
//         subject,
//         htmlbody,
//     });
// };

// module.exports = {
//     sendEmail,
// };

/* eslint-disable camelcase */
/* eslint-disable id-length */
// emailService.js

const fs = require('fs');
const { SendMailClient } = require('zeptomail');
const dotenv = require('dotenv');

dotenv.config();

const SOURCE = process.env.SOURCE;
const url = process.env.EMAIL_URL;
const token = process.env.EMAIL_TOKEN;

const client = new SendMailClient({ url, token });

/**
 * Generic function to send an email with optional PDF attachment.
 * @param {string} toEmail - The recipient's email address.
 * @param {string} subject - The subject of the email.
 * @param {string} htmlbody - The HTML content of the email.
 * @param {string} [pdfPath] - Optional path to a PDF file to attach.
 */
const sendEmail = async (
    toEmail,
    subject,
    htmlbody,
    pdfPath,
    referenceCode
) => {
    const mailOptions = {
        from: {
            address: SOURCE,
            name: 'noreply',
        },
        to: [
            {
                email_address: {
                    address: toEmail,
                },
            },
        ],
        subject,
        htmlbody,
    };

    if (pdfPath && fs.existsSync(pdfPath)) {
        const fileContent = fs.readFileSync(pdfPath).toString('base64');
        mailOptions.attachments = [
            {
                content: fileContent,
                name: `${referenceCode}.pdf`,
                mime_type: 'application/pdf',
            },
        ];
    }

    await client.sendMail(mailOptions);
};

module.exports = {
    sendEmail,
};
