/**
 * @module router
 * @description Main API routes for the application.
 * Groups routes for authentication, events, and registration.
 */

const express = require('express');

const categoryRouter = require('./v1/category/categoryRouter');

const subCategoryRouter = require('./v1/subCategory/subCategoryRouter');

const serviceInfoRouter = require('./v1/serviceInfo/serviceInfoRouter');

const staffRouter = require('./v1/staff/staffRouter');

const bookingRouter = require('./v1/booking/bookingRouter');

const packagesRouter = require('./v1/packages/packagesRouter');

const reviewRouter = require('./v1/Reviews/reviewRouter');

const router = express.Router();

router.use('/v1/category', categoryRouter);

router.use('/v1/subcategory', subCategoryRouter);

router.use('/v1/service', serviceInfoRouter);

router.use('/v1/staff', staffRouter);

router.use('/v1/booking', bookingRouter);

router.use('/v1/packAndDis', packagesRouter);

router.use('/v1/reviews', reviewRouter);

module.exports = router;
