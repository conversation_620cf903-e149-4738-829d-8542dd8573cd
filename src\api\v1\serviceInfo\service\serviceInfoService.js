/* eslint-disable no-underscore-dangle */
/* eslint-disable id-length */
const ServiceInfo = require('../model/serviceInfoModel');
const dotenv = require('dotenv');
const ServiceInfoCounter = require('../counter/serviceInfoCounter');

const relatedDataService = require('./relatedDataService');

const serviceInfoFetcher = require('./serviceInfoFetcher');

const StaffService = require('../../staff/service/staffService');

const logger = require('../../../common/utils/logger');

const packageService = require('../../packages/service/packagesService');

const { getUserById } = require('../../../common/config/communicateDB');

const {
    searchDocuments,
    indexDocument,
    updateDocument,
    deleteDocument,
} = require('../../../common/searchfunc/elasticsearchHelpers');

dotenv.config();

const discountService = require('../../packages/service/discountService');

const createServiceInformation = async (
    serviceData,
    providerId,
    providerName,
    providerEmail,
    providerPhoneNumber
) => {
    let serviceId;

    try {
        // Generate unique service ID
        const sid = await ServiceInfoCounter.getNextSequence();
        serviceId = `SID_${sid}`;

        // Prepare online meeting platforms
        const onlineMeetings = [];

        if (serviceData.allowZoomMeeting && serviceData.zoomInvitationLink) {
            onlineMeetings.push({
                platform: 'Zoom',
                invitationLink: serviceData.zoomInvitationLink,
                allowMeeting: true,
            });
        }

        if (serviceData.allowGoogleMeet && serviceData.googleInvitationLink) {
            onlineMeetings.push({
                platform: 'GoogleMeet',
                invitationLink: serviceData.googleInvitationLink,
                allowMeeting: true,
            });
        }

        // Handle staff assignment and potential provider creation
        let staff = Array.isArray(serviceData.staff) ? serviceData.staff : [];
        let providerStaffId = null;

        if (staff.includes(providerId)) {
            const staffList = await StaffService.getAllStaff();

            const existingProvider = staffList.find(
                (staff) => staff.providerId === providerId && staff.isProvider
            );

            if (!existingProvider) {
                const staffData = {
                    providerId,
                    fullName: providerName,
                    email: providerEmail,
                    phoneNumber: providerPhoneNumber,
                    address: serviceData.address || '',
                    country: serviceData.country || '',
                    state: serviceData.state || '',
                    city: serviceData.city || '',
                    zipCode: serviceData.zipCode || '',
                    description: serviceData.description || '',
                    serviceIds: [serviceId],
                    isProvider: true,
                };

                logger.info(
                    `Creating new provider staff: ${JSON.stringify(staffData)}`
                );
                const createdStaff = await StaffService.createStaff(
                    staffData,
                    providerId
                );
                providerStaffId =
                    createdStaff?.staffId || createdStaff?.id || null;
            } else {
                logger.info(`Provider ${providerId} already exists as staff.`);
                providerStaffId =
                    existingProvider?.staffId || existingProvider?.id || null;
            }

            // Replace providerId with staff ID in staff list
            staff = staff.map((id) =>
                id === providerId ? providerStaffId : id
            );
        } else {
            logger.info(
                `Provider ${providerId} not included in staff. Skipping staff creation.`
            );
        }

        // Handle discount logic
        let discountId = null;

        if (
            serviceData.discount &&
            typeof serviceData.discount === 'object' &&
            serviceData.discount.amount > 0
        ) {
            const {
                discountType,
                valueType,
                durationType,
                amount,
                duration,
                promoCode,
                maxCount,
            } = serviceData.discount;

            const discountData = {
                serviceId,
                serviceName: serviceData.serviceTitle,
                isDiscount: true,
                discountType,
                valueType,
                durationType,
                amount,
                duration,
                promoCode,
                maxCount,
                providerId,
            };

            const discountDetails = await discountService.createDiscount(
                discountData,
                providerId,
                false
            );

            if (discountDetails?.discountId) {
                discountId = discountDetails.discountId;
                logger.info(
                    `Discount created for service ${serviceId}: ${discountId}`
                );
            } else {
                logger.warn(
                    `Discount created but no discountId returned for ${serviceId}`
                );
            }
        }

        let packageId;

        if (serviceData.packages && serviceData.packages.length > 0) {
            const packagePromises = serviceData.packages.map((pkg) => {
                const packageData = {
                    ...pkg,
                    providerId,
                    serviceId,
                    serviceName: serviceData.serviceTitle,
                };
                return packageService.createPackage(
                    packageData,
                    providerId,
                    false
                );
            });

            const packageResults = await Promise.all(packagePromises);

            const packageIds = packageResults
                .filter((res) => res && res.packageId)
                .map((res) => res.packageId);

            if (packageIds.length > 0) {
                packageId = packageIds;
            }
        }

        // Create service document
        const service = new ServiceInfo({
            serviceId,
            providerId,
            serviceTitle: serviceData.serviceTitle,
            slug: serviceData.slug,
            categoryId: serviceData.categoryId,
            subCategoryId: serviceData.subCategoryId,
            price: serviceData.price,
            offerPrice: serviceData.offerPrice,
            isOffers: serviceData.isOffers,
            priceAfterDiscount: serviceData.priceAfterDiscount,
            duration: serviceData.duration,
            onlineMeetings,
            staff,
            includes: serviceData.includes,
            serviceOverview: serviceData.serviceOverview,
            isActive: serviceData.isActive,
            isAdditional: serviceData.isAdditional,
            additionalServicesId: [],
            serviceAvailableId: [],
            locationId: [],
            galleryId: [],
            faqId: [],
            seoId: [],
            packageId: packageId || [],
            discountId,
            isDiscount: serviceData.isDiscount,
            isPackage: serviceData.isPackage,
        });

        const savedService = await service.save();

        // Update staff service IDs
        await StaffService.updateStaffServiceIds(service.staff, serviceId);

        logger.info(`Service created with ID: ${serviceId}`);

        // Attach related data (gallery, location, etc.)
        const relatedData = await relatedDataService.handleRelatedData(
            serviceId,
            serviceData
        );

        savedService.additionalServicesId =
            relatedData.additionalServicesId || [];
        savedService.serviceAvailableId = relatedData.serviceAvailableIds || [];
        savedService.locationId = relatedData.locationIds || [];
        savedService.galleryId = relatedData.galleryIds || [];
        savedService.faqId = relatedData.faqIds || [];
        savedService.seoId = relatedData.seoIds || [];

        await savedService.save();
        logger.info(`Service ${serviceId} updated with related data.`);

        // Index to Elastic
        const finaldataConvert = { ...serviceData, serviceId };
        indexDocument(process.env.ELASTIC_INDEX, service._id, finaldataConvert);

        return savedService;
    } catch (error) {
        logger.error(`Error creating service ${serviceId}: ${error.message}`);

        try {
            await deleteServiceInformation(serviceId);
            logger.info(`Rolled back service creation for ID: ${serviceId}`);
        } catch (rollbackErr) {
            logger.error(
                `Rollback failed for service ${serviceId}: ${rollbackErr.message}`
            );
        }

        throw new Error('Error creating service information: ' + error.message);
    }
};

const getServiceInformation = async (
    query,
    sortBy,
    sortDirection,
    pageNum,
    limitNum
) => {
    const skip = (pageNum - 1) * limitNum;

    if (![1, -1].includes(sortDirection)) {
        throw new Error(
            'Invalid sort direction. Use 1 for ascending and -1 for descending.'
        );
    }

    try {
        const services = await ServiceInfo.find(query)
            .sort({ updatedAt: -1, [sortBy]: sortDirection })
            .skip(skip)
            .limit(limitNum);

        const total = await ServiceInfo.countDocuments(query);

        const serviceIds = services.map((service) => service.serviceId);

        const allServiceData = await getAllServiceData(serviceIds);

        return { services: allServiceData, total };
    } catch (error) {
        logger.error('Error fetching services with details:', error);
        throw new Error('Failed to retrieve services with details.');
    }
};

const getServiceInformationById = async (serviceId) => {
    try {
        const service = await ServiceInfo.findOne({ serviceId });
        if (!service) {
            logger.warn(`Service with ID: ${serviceId} not found.`);
            throw new Error('Service not found.');
        }

        const availability =
            await serviceInfoFetcher.fetchServiceAvailability(service);

        const formattedAvailability = formatAvailability(availability);

        const packages =
            service.packageId && service.packageId.length > 0
                ? await Promise.all(
                      service.packageId.map((id) =>
                          packageService.getPackageById(id)
                      )
                  )
                : [];

        const staff =
            service.staff && service.staff.length > 0
                ? await Promise.all(
                      service.staff.map((id) => StaffService.getStaffById(id))
                  )
                : [];

        const data = staff.map((member) => ({
            staffId: member.staffId,
            fullName: member.fullName,
            email: member.email,
            city: member.city,
            active: member.status,
        }));

        const providerData = await getUserById(service.providerId);

        const providerName = providerData.user.name;
        const providerProfilePic = providerData.user.profilePicture;
        const providerStatus = providerData.user.IsActive;
        const fullServiceData = {
            _id: service._id,
            serviceId: service.serviceId,
            providerId: service.providerId,
            providerName,
            providerProfilePic,
            providerStatus,
            serviceTitle: service.serviceTitle,
            slug: service.slug,
            categoryId: service.categoryId,
            subCategoryId: service.subCategoryId,
            price: service.price,
            isOffers: service.isOffers,
            offerPrice: service.offerPrice,
            priceAfterDiscount: service.priceAfterDiscount,
            duration: service.duration,
            allowZoomMeeting: service.allowZoomMeeting,
            zoomInvitationLink: service.zoomInvitationLink,
            allowGoogleMeet: service.allowGoogleMeet,
            googleInvitationLink: service.googleInvitationLink,
            staff: service.staff,
            includes: service.includes,
            serviceOverview: service.serviceOverview,
            isActive: service.isActive,
            isAdditional: service.isAdditional,
            isDiscount: service.isDiscount,
            isPackage: service.isPackage,
            additionalServices:
                await serviceInfoFetcher.fetchAdditionalServices(service),
            availability: formattedAvailability.availability,
            location: await serviceInfoFetcher.fetchLocation(service),
            gallery: await serviceInfoFetcher.fetchGallery(service),
            faq: await serviceInfoFetcher.fetchFaq(service),
            seo: await serviceInfoFetcher.fetchSeo(service),
            discount:
                (await discountService.getDiscountById(service.discountId)) ||
                null,
            packages,
        };

        logger.info(`Fetched full service data for service ID: ${serviceId}`);
        return fullServiceData;
    } catch (error) {
        logger.error(
            `Error retrieving service by ID: ${serviceId} - ${error.message}`
        );
        throw error;
    }
};

const getAllServiceInformation = async (query) => {
    const searchResults = await searchDocuments(
        process.env.ELASTIC_INDEX,
        query
    );
    logger.info(`Search results: ${searchResults}`);
    const totalRecords = searchResults.hits?.total.value;
    const finalResults = searchResults.hits?.hits;
    const sortedResults = finalResults?.map((result) => result._source);
    // const sortedResults = searchResults.sort((a, b) => b._source.rating - a._source.rating);

    return { sortedResults, totalRecords };
};

const updateServiceInformation = async (serviceId, updatedData) => {
    try {
        logger.info(
            `Updating service with ID: ${serviceId} with data: ${JSON.stringify(
                updatedData
            )}`
        );
        const service = await ServiceInfo.findOne({ serviceId });
        if (!service) {
            logger.warn(`Service with ID: ${serviceId} not found.`);
            throw new Error('Service not found');
        }

        const updatedFields = {};
        let hasChanges = false;

        const updateFieldIfChanged = (field, newValue) => {
            // Check if newValue is explicitly provided and different from the current value.
            if (newValue !== undefined && newValue !== service[field]) {
                updatedFields[field] = newValue;
                hasChanges = true;
            }
        };

        updateFieldIfChanged('serviceTitle', updatedData.serviceTitle);
        updateFieldIfChanged('slug', updatedData.slug);
        updateFieldIfChanged('categoryId', updatedData.categoryId);
        updateFieldIfChanged('subCategoryId', updatedData.subCategoryId);
        updateFieldIfChanged('price', updatedData.price);
        updateFieldIfChanged('offerPrice', updatedData.offerPrice);
        updateFieldIfChanged(
            'priceAfterDiscount',
            updatedData.priceAfterDiscount
        );
        updateFieldIfChanged('duration', updatedData.duration);
        updateFieldIfChanged('staff', updatedData.staff);
        updateFieldIfChanged('includes', updatedData.includes);
        updateFieldIfChanged('serviceOverview', updatedData.serviceOverview);
        updateFieldIfChanged('isActive', updatedData.isActive);
        updateFieldIfChanged('isOffers', updatedData.isOffers);
        updateFieldIfChanged('isAdditional', updatedData.isAdditional);
        updateFieldIfChanged('isPackage', updatedData.isPackage);
        updateFieldIfChanged('isDiscount', updatedData.isDiscount);

        const onlineMeetingsUpdate = [];

        if (updatedData.allowZoomMeeting && updatedData.zoomInvitationLink) {
            onlineMeetingsUpdate.push({
                platform: 'Zoom',
                invitationLink: updatedData.zoomInvitationLink,
                allowMeeting: updatedData.allowZoomMeeting,
            });
        }

        if (updatedData.allowGoogleMeet && updatedData.googleInvitationLink) {
            onlineMeetingsUpdate.push({
                platform: 'GoogleMeet',
                invitationLink: updatedData.googleInvitationLink,
                allowMeeting: updatedData.allowGoogleMeet,
            });
        }

        if (onlineMeetingsUpdate.length > 0) {
            updatedFields['onlineMeetings'] = onlineMeetingsUpdate;
            hasChanges = true;
        }

        const relatedDataUpdates = [];

        if (
            Array.isArray(updatedData.additionalService) &&
            updatedData.additionalService.length === 0
        ) {
            if (
                Array.isArray(service.additionalServicesId) &&
                service.additionalServicesId.length > 0
            ) {
                await relatedDataService.deleteAdditionalService(
                    service.additionalServicesId
                );
            }
        }

        await serviceInfoFetcher.updateRelatedData(
            'additionalServicesId',
            relatedDataService.saveAdditionalService,
            updatedData.additionalService,
            serviceId,
            updatedFields,
            relatedDataUpdates
        );
        await serviceInfoFetcher.updateRelatedData(
            'serviceAvailableId',
            relatedDataService.saveAvailability,
            updatedData.availability,
            serviceId,
            updatedFields,
            relatedDataUpdates
        );
        await serviceInfoFetcher.updateRelatedData(
            'locationId',
            relatedDataService.saveLocations,
            updatedData.location,
            serviceId,
            updatedFields,
            relatedDataUpdates
        );
        await serviceInfoFetcher.updateRelatedData(
            'galleryId',
            relatedDataService.saveGallery,
            updatedData.gallery,
            serviceId,
            updatedFields,
            relatedDataUpdates
        );
        await serviceInfoFetcher.updateRelatedData(
            'faqId',
            relatedDataService.saveFaq,
            updatedData.faq,
            serviceId,
            updatedFields,
            relatedDataUpdates
        );
        await serviceInfoFetcher.updateRelatedData(
            'seoId',
            relatedDataService.saveSEO,
            updatedData.seo,
            serviceId,
            updatedFields,
            relatedDataUpdates
        );

        // Handle Discount Update or Removal
        if ('discount' in updatedData) {
            if (updatedData.discount && updatedData.discount.amount > 0) {
                const discountPayload = {
                    serviceId,
                    serviceName:
                        updatedData.serviceTitle || service.serviceTitle,
                    isDiscount: true,
                    discountType: updatedData.discount.discountType,
                    valueType: updatedData.discount.valueType,
                    durationType: updatedData.discount.durationType,
                    amount: updatedData.discount.amount,
                    duration: {
                        start: updatedData.discount.duration?.start,
                        end: updatedData.discount.duration?.end,
                    },
                    promoCode: updatedData.discount.promoCode,
                    maxCount: updatedData.discount.maxCount,
                };

                // If there's already a discountId → update existing
                if (service.discountId) {
                    await discountService.updateDiscount(
                        service.discountId,
                        discountPayload
                    );
                    logger.info(`Discount updated for service ${serviceId}`);
                    updatedFields.discountId = service.discountId;
                } else {
                    // Create new discount
                    const discountDetails =
                        await discountService.createDiscount(
                            discountPayload,
                            service.providerId
                        );
                    updatedFields.discountId = discountDetails.discountId;
                    logger.info(
                        `Discount created for service ${serviceId}: ${discountDetails.discountId}`
                    );
                }

                hasChanges = true;
            } else if (service.discountId) {
                // No discount in request but one exists in DB → remove it
                await discountService.deleteDiscount(service.discountId);

                updatedFields.discountId = null;
                hasChanges = true;
                logger.info(
                    `Existing discount removed for service ${serviceId}`
                );
            }
        }

        if ('packages' in updatedData) {
            // Extract only numeric keys from updatedData.packages and convert to array
            const cleanedPackages = Object.entries(updatedData.packages)
                .filter(([key]) => !isNaN(key)) // keep only numeric keys like "0", "1", ...
                .map(([, value]) => value);

            const updatePromises = cleanedPackages.map((pkg, index) => {
                const updateData = {
                    serviceId,
                    packageName: pkg.packageName,
                    price: pkg.price,
                    includes: pkg.includes,
                    isSoldOut: pkg.isSoldOut,
                };

                if (pkg.discount) {
                    updateData.discount = {
                        isDiscount: pkg.discount.isDiscount,
                        discountType: pkg.discount.discountType,
                        valueType: pkg.discount.valueType,
                        durationType: pkg.discount.durationType,
                        amount: pkg.discount.amount,
                        duration: {
                            start: pkg.discount.duration?.start,
                            end: pkg.discount.duration?.end,
                        },
                        promoCode: pkg.discount.promoCode,
                        maxCount: pkg.discount.maxCount,
                        isPackageDiscount: pkg.discount.isPackageDiscount,
                        IsActive: pkg.discount.IsActive,
                    };
                }

                return packageService.updatePackageById(
                    pkg.packageId,
                    updateData
                );
            });

            try {
                const results = await Promise.all(updatePromises);
            } catch (err) {
                console.error('Error updating one or more packages:', err);
            }
        }

        if (hasChanges || relatedDataUpdates.length > 0) {
            const updatedService = await ServiceInfo.findOneAndUpdate(
                { serviceId },
                { $set: updatedFields },
                { new: true }
            );

            if (updatedService) {
                logger.info(
                    `Service with ID: ${serviceId} updated successfully.`
                );
                if (hasChanges) {
                    logger.info(
                        `Updated fields: ${Object.keys(updatedFields).join(', ')}`
                    );
                }
                if (relatedDataUpdates.length > 0) {
                    logger.info(
                        `Updated related data: ${relatedDataUpdates.join(', ')}`
                    );
                }

                const result = await updateDocument(
                    process.env.ELASTIC_INDEX,
                    service._id,
                    updatedFields
                );

                if (result.result !== 'updated') {
                    logger.warn(
                        `Service with ID: ${serviceId} was not updated in Elasticsearch.`
                    );
                }

                logger.info(
                    `Service with ID: ${serviceId} updated in Elasticsearch.`
                );

                return updatedService;
            } else {
                logger.warn(`Service with ID: ${serviceId} was not updated.`);
                throw new Error('Failed to update service.');
            }
        } else {
            logger.info(
                `No changes detected for service with ID: ${serviceId}`
            );
            return service;
        }
    } catch (error) {
        logger.error(
            `Error updating service with ID: ${serviceId} - ${error.message}`
        );
        throw new Error(error.message);
    }
};

const deleteServiceInformation = async (serviceId) => {
    if (!serviceId) {
        logger.error('Service ID is required.');
        throw new Error('Service ID is required.');
    }

    try {
        const service = await ServiceInfo.findOne({ serviceId });

        if (!service) {
            logger.warn(`Service with ID: ${serviceId} not found.`);
            throw new Error('Service not found.');
        }

        const {
            additionalServicesId,
            serviceAvailableId,
            locationId,
            galleryId,
            faqId,
            seoId,
        } = service;

        const deletePromises = [];

        if (additionalServicesId && additionalServicesId.length > 0) {
            deletePromises.push(
                relatedDataService.deleteAdditionalService(additionalServicesId)
            );
        }
        if (serviceAvailableId && serviceAvailableId.length > 0) {
            deletePromises.push(
                relatedDataService.deleteAvailability(serviceAvailableId)
            );
        }
        if (locationId && locationId.length > 0) {
            deletePromises.push(relatedDataService.deleteLocations(locationId));
        }
        if (galleryId && galleryId.length > 0) {
            deletePromises.push(relatedDataService.deleteGallery(galleryId));
        }
        if (galleryId && galleryId.length > 0) {
            deletePromises.push(relatedDataService.deleteFaq(faqId));
        }
        if (seoId && seoId.length > 0) {
            deletePromises.push(relatedDataService.deleteSEO(seoId));
        }
        if (service.discountId) {
            await discountService.deleteDiscount(service.discountId);
        }

        await Promise.all(deletePromises);
        logger.info(
            `Related data for service ID: ${serviceId} has been deleted.`
        );

        await StaffService.removeServiceIdFromStaff(service.staff, serviceId);

        const deletedService = await ServiceInfo.findOneAndDelete({
            serviceId,
        });

        if (!deletedService) {
            logger.warn(`Service with ID: ${serviceId} was not deleted.`);
            throw new Error('Service not deleted.');
        }
        const result = await deleteDocument(
            process.env.ELASTIC_INDEX,
            service._id
        );
        if (result.result !== 'deleted') {
            logger.warn(
                `Service with ID: ${serviceId} was not deleted from Elasticsearch.`
            );
        }

        logger.info(
            `Service with ID: ${serviceId} has been deleted from Elasticsearch.`
        );
        logger.info(`Service with ID: ${serviceId} has been deleted.`);

        return deletedService;
    } catch (error) {
        logger.error(
            `Error deleting service with ID: ${serviceId} - ${error.message}`
        );
        throw new Error(error.message);
    }
};

const getAllServiceData = async (serviceIds) => {
    try {
        const serviceDataPromises = serviceIds.map((serviceId) =>
            getServiceInformationById(serviceId)
        );
        const services = await Promise.all(serviceDataPromises);

        return services;
    } catch (error) {
        logger.error(`Error fetching all service data: ${error.message}`);
        throw new Error('Failed to retrieve all service data.');
    }
};

async function checkAndAddStaff(existingStaffIds, newStaffIds, serviceId) {
    for (const newStaffId of newStaffIds) {
        if (!existingStaffIds.includes(newStaffId)) {
            await StaffService.updateStaffServiceIds(newStaffId, serviceId);
            logger.info(`New staff ID ${newStaffId} can be added.`);
        }
    }

    for (const existingStaffId of existingStaffIds) {
        if (!newStaffIds.includes(existingStaffId)) {
            await StaffService.removeServiceIdFromStaff(
                existingStaffId,
                serviceId
            );
            logger.warn(
                `Staff with ID ${existingStaffId} is no longer included in the new list.`
            );
        }
    }
}

// const formatAvailability = (availability) => {
//     const formattedAvailability = {
//         Monday: [],
//         Tuesday: [],
//         Wednesday: [],
//         Thursday: [],
//         Friday: [],
//         Saturday: [],
//         Sunday: [],
//     };

//     let alldate = true;
//     let referenceSlots = null;

//     const compareTimeSlots = (slots1, slots2) => {
//         if (slots1.length !== slots2.length) return false;
//         return slots1.every((slot, index) => {
//             return (
//                 slot.from === slots2[index].from &&
//                 slot.to === slots2[index].to &&
//                 slot.maxBookings === slots2[index].maxBookings
//             );
//         });
//     };

//     availability.forEach((item) => {
//         const dayName = item.day.charAt(0).toUpperCase() + item.day.slice(1);

//         if (formattedAvailability[dayName] && Array.isArray(item.timeSlots)) {
//             if (referenceSlots === null) {
//                 referenceSlots = item.timeSlots;
//             }

//             item.timeSlots.forEach((slot) => {
//                 formattedAvailability[dayName].push({
//                     id: item.id,
//                     from: slot.from,
//                     to: slot.to,
//                     slots: slot.maxBookings,
//                     availableSlots: slot.availableSlots,
//                     booked: slot.booked,
//                 });
//             });

//             // Only use this block if bookingTimeSlots actually exist in your data

//             if (!compareTimeSlots(referenceSlots, item.timeSlots)) {
//                 alldate = false;
//             }
//         } else {
//             console.warn(
//                 `Missing or malformed timeSlots for item with ID: ${item.id}`
//             );
//         }
//     });

//     const allDaysHaveSlots = Object.values(formattedAvailability).every(
//         (day) => day.length > 0
//     );
//     if (!allDaysHaveSlots) {
//         alldate = false;
//     }

//     const updatedAvailability = availability.map((item) => ({
//         ...item,
//         alldate: alldate,
//     }));

//     return {
//         availability: updatedAvailability,
//         formattedAvailability,
//     };
// };
const formatAvailability = (availability) => {
    const daysOfWeek = [
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
        'Sunday',
    ];

    const formattedAvailability = Object.fromEntries(
        daysOfWeek.map((day) => [day, []])
    );

    let alldate = true;
    let referenceSlots = null;

    const compareTimeSlots = (slots1, slots2) => {
        if (slots1.length !== slots2.length) return false;
        return slots1.every(
            (slot, i) =>
                slot.from === slots2[i].from &&
                slot.to === slots2[i].to &&
                slot.maxBookings === slots2[i].maxBookings
        );
    };

    const transformedAvailability = availability.map((item) => {
        const slots = item.timeSlots;

        // If referenceSlots is null, assign first item's slots to compare with others
        if (referenceSlots === null) referenceSlots = slots;

        // Compare current slots with referenceSlots, update alldate accordingly
        if (!compareTimeSlots(referenceSlots, slots)) alldate = false;

        // Compose combined time slot (from first slot's start to last slot's end)
        const combinedSlot = {
            from: slots[0]?.from || '',
            to: slots[slots.length - 1]?.to || '',
            maxBookings: slots.length,
            availableSlots: slots.reduce(
                (sum, s) => sum + (s.availableSlots || 0),
                0
            ),
            booked: slots.reduce((sum, s) => sum + (s.booked || 0), 0),
        };

        // Format day name properly
        const dayName =
            item.day.charAt(0).toUpperCase() + item.day.slice(1).toLowerCase();

        // Add combined slot to formattedAvailability
        if (formattedAvailability[dayName]) {
            formattedAvailability[dayName].push({
                id: item.id,
                ...combinedSlot,
            });
        } else {
            console.warn(`Day ${dayName} is invalid or not recognized.`);
        }

        // Return transformed entry with a single combined slot
        return {
            id: item.id,
            day: item.day,
            timeSlots: [combinedSlot],
        };
    });

    // Check if every day has at least one slot; if not, alldate is false
    const allDaysHaveSlots = Object.values(formattedAvailability).every(
        (slots) => slots.length > 0
    );
    if (!allDaysHaveSlots) alldate = false;

    // Add alldate flag to all transformed availability entries
    const updatedAvailability = transformedAvailability.map((item) => ({
        ...item,
        alldate,
    }));

    return {
        availability: updatedAvailability,
        formattedAvailability,
    };
};

module.exports = {
    createServiceInformation,
    getServiceInformation,
    getServiceInformationById,
    updateServiceInformation,
    getAllServiceInformation,
    deleteServiceInformation,
    checkAndAddStaff,
    formatAvailability,
};
