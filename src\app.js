const express = require('express');

const bodyParser = require('body-parser');

const cors = require('cors');

const dotenv = require('dotenv');

const cookieParser = require('cookie-parser');

const routes = require('./api/router');

const YAML = require('yamljs');

const swaggerUi = require('swagger-ui-express');

const path = require('path');

const logger = require('./api/common/utils/logger');

const { inprogressSchedule } = require('./api/common/utils/inprogressSchedule');

dotenv.config();

const app = express();

const swaggerDocument = YAML.load(path.join(__dirname, 'OpenAPI.yaml'));

app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument));

app.use(bodyParser.json());

app.use(cookieParser());

const corsOptions = {
    origin: process.env.FRONTEND_DOMAIN,
    methods: 'GET,POST,PUT,DELETE,OPTIONS',
    allowedHeaders: 'Content-Type, Authorization, X-user-type',
    credentials: true,
};

app.use(cors(corsOptions));

inprogressSchedule();

app.use('/api', routes);

app.get('/test', (req, res) => {
    res.send('Hello World!');
});

app.get('/api/reverse-geocode', async (req, res) => {
    const { lat, lon } = req.query;
    logger.info('Reverse geocode request received:', { lat, lon });
    try {
        const response = await fetch(
            `https://api.geoapify.com/v1/geocode/reverse?lat=${lat}&lon=${lon}&lang=fr&apiKey=${process.env.GEOPIFY_APIKEY}`
        );
        const data = await response.json();
        res.json(data);
    } catch (error) {
        logger.error('Error fetching reverse geocode data:', error);
        res.status(500).json({ error: 'Failed to fetch data' });
    }
});

app.use((req, res, next) => {
    logger.info('Request received:', req.method, req.ip);
    next();
});

module.exports = app;
