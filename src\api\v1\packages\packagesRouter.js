const express = require('express');

const router = express.Router();

const PackageController = require('./packagesController');

const PackageMiddleware = require('./packagesMiddleware');

const fetchAuth = require('../../common/utils/communicator');

// ==============================
// 🔹 CREATE
// ==============================

// Create a product (either discount or packages)
router.post(
    '/',
    PackageMiddleware.conditionalValidator,
    fetchAuth.fetchAuthProviderDataMiddleware,
    PackageController.createPackagesAndDiscount
);

router.post(
    '/checkPromoCode',
    fetchAuth.fetchAuthProviderDataMiddleware,
    PackageController.checkPromoCodeExists
);

// ==============================
// 🔹 READ (GET)
// ==============================

router.get(
    '/',
    PackageMiddleware.routeIdentifier('default'),
    PackageController.getPackagesAndDiscounts
);

// Get all discounts
router.get(
    '/discounts',
    PackageMiddleware.routeIdentifier('default'),
    PackageController.getPackagesAndDiscount
);

// Get all packages
router.get(
    '/packages',
    PackageMiddleware.routeIdentifier('default'),
    PackageController.getPackagesAndDiscount
);

router.get(
    '/discounts/providerDiscounts',
    fetchAuth.fetchAuthProviderDataMiddleware,
    PackageMiddleware.routeIdentifier('provider'),
    PackageController.getPackagesAndDiscount
);

// Get all packages
router.get(
    '/packages/providerPackages',
    fetchAuth.fetchAuthProviderDataMiddleware,
    PackageMiddleware.routeIdentifier('provider'),
    PackageController.getPackagesAndDiscount
);

// Get single product by ID (can be either discount or package set)
router.get(
    '/packages/:productId',
    PackageController.getPackagesAndDiscountById
);

// Get single discount by ID (only if isPackage === false)
router.get(
    '/discounts/:discountId',
    PackageController.getPackagesAndDiscountById
);

// ==============================
// 🔹 UPDATE
// ==============================

// Update a product (either discount or packages)
router.put(
    '/packages/:productId',

    fetchAuth.fetchAuthProviderDataMiddleware,
    PackageController.UpdatePackagesAndDiscountById
);

// Update a discount by ID (isPackage === false)
router.put(
    '/discounts/:discountId',

    fetchAuth.fetchAuthProviderDataMiddleware,
    PackageController.UpdatePackagesAndDiscountById
);

// ==============================
// 🔹 DELETE
// ==============================

// Delete a product (either discount or packages)
router.delete(
    '/packages/:productId',
    fetchAuth.fetchAuthProviderDataMiddleware,
    PackageController.deletePackagesAndDiscountById
);

// Delete a discount by ID (isPackage === false)
router.delete(
    '/discounts/:discountId',
    fetchAuth.fetchAuthProviderDataMiddleware,
    PackageController.deletePackagesAndDiscountById
);

module.exports = router;
