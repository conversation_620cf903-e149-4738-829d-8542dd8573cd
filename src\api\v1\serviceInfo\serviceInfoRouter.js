const express = require('express');

const router = express.Router();

const ServiceInformationController = require('./serviceInfoController');

const fetchAuth = require('../../common/utils/communicator');

const serviceMiddleware = require('./serviceInfoMiddleware');

router.post(
    '/',
    fetchAuth.fetchAuthProviderDataMiddleware,
    ServiceInformationController.createServiceInformation
);

router.get(
    '/',
    serviceMiddleware.routeIdentifier('default'),
    ServiceInformationController.getServiceInformation
);
router.get(
    '/providerService',
    fetchAuth.fetchAuthProviderDataMiddleware,
    serviceMiddleware.routeIdentifier('provider'),
    ServiceInformationController.getServiceInformation
);

router.get(
    '/provider/:userId',
    fetchAuth.fetchAuthProviderDataMiddleware,
    serviceMiddleware.routeIdentifier('provider'),
    ServiceInformationController.getServiceInformation
);

router.get('/getstates', ServiceInformationController.getCountryStates);

router.get(
    '/getService',
    ServiceInformationController.getAllServiceInformation
);

router.get(
    '/:serviceId',
    ServiceInformationController.getServiceInformationById
);

router.put(
    '/:serviceId',
    fetchAuth.fetchAuthProviderDataMiddleware,
    ServiceInformationController.updateServiceInformation
);

router.delete(
    '/:serviceId',
    fetchAuth.fetchAuthProviderDataMiddleware,
    ServiceInformationController.deleteServiceInformation
);

module.exports = router;
