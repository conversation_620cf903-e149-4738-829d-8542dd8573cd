const cron = require('node-cron');
const logger = require('./logger');
const bookingService = require('../../v1/booking/service/bookingService');

const inprogressSchedule = () => {
    cron.schedule('* * * * *', async () => {
        try {
            const now = new Date();

            logger.info(
                `Scheduled task running at ${now.toISOString()} - checking for confirmed bookings to update.`
            );

            const bookings = await bookingService.getConfirmedBookings();

            logger.info(
                `Found ${bookings.length} bookings with status 'confirmed'.`
            );

            for (const booking of bookings) {
                const appointmentDate = new Date(booking.appointmentDate);
                const timeFromString = booking.appointmentTimeFrom;

                // Guard clause: skip if appointmentTimeFrom is missing or invalid
                if (
                    !timeFromString ||
                    typeof timeFromString !== 'string' ||
                    !/^\d{1,2}:\d{2} (AM|PM)$/i.test(timeFromString)
                ) {
                    logger.warn(
                        `Skipping booking ${booking.bookingId} due to invalid appointmentTimeFrom format: ${timeFromString}`
                    );
                    continue;
                }

                // Safe to split now
                const [time, meridian] = timeFromString.split(' ');
                let [hours, minutes] = time.split(':').map(Number);

                if (meridian.toUpperCase() === 'PM' && hours !== 12)
                    hours += 12;
                if (meridian.toUpperCase() === 'AM' && hours === 12) hours = 0;

                // Construct appointment start datetime
                const appointmentStart = new Date(
                    appointmentDate.getFullYear(),
                    appointmentDate.getMonth(),
                    appointmentDate.getDate(),
                    hours,
                    minutes,
                    0,
                    0
                );

                // Check if now and appointment start are the same minute
                const isSameMinute =
                    now.getFullYear() === appointmentStart.getFullYear() &&
                    now.getMonth() === appointmentStart.getMonth() &&
                    now.getDate() === appointmentStart.getDate() &&
                    now.getHours() === appointmentStart.getHours() &&
                    now.getMinutes() === appointmentStart.getMinutes();

                if (isSameMinute) {
                    await bookingService.updateBookingStatus(
                        booking.bookingId,
                        'InProgress',
                        'System auto-update to InProgress',
                        'system'
                    );

                    logger.info(
                        `Booking ${booking.bookingId} status updated to 'InProgress'.`
                    );
                }
            }
        } catch (err) {
            logger.error('Error during scheduled in-progress task:', err);
        }
    });
};

module.exports = { inprogressSchedule };
